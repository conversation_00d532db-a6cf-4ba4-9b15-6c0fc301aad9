import { AngularNodeAppEngine, createNodeRequestHand<PERSON>, isMainModule, writeResponseToNodeResponse } from '@angular/ssr/node'
import dotenv from 'dotenv'
import express from 'express'
import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'
import 'zone.js/node'

dotenv.config();

export function app(): express.Express {
  const server = express();
  const serverDistFolder = dirname(fileURLToPath(import.meta.url));
  const browserDistFolder = resolve(serverDistFolder, '../browser');

  const angularNodeAppEngine = new AngularNodeAppEngine();

  server.get(
    '**',
    express.static(browserDistFolder, {
      maxAge: '1y',
      index: 'index.html',
    })
  );

  // All regular routes use the Angular engine
  server.get('*', (req, res, next) => {
    angularNodeAppEngine
      .handle(req, { server: 'express' })
      .then((response: Response | null) => {
        if (response) {
          writeResponseToNodeResponse(response, res);
        } else {
          next();
        }
      })
      .catch((error: any) => {
        console.error('Angular SSR Error:', error);
        next(error);
      });
  });

  return server;
}

const server = app();

if (isMainModule(import.meta.url)) {
  const port = process.env['CLIENT_PORT'] ? parseInt(process.env['CLIENT_PORT']) : 4000;
  const host = process.env['HOST'] || 'localhost';
  server.listen(port, host, () => {
    console.log(`Node Express server listening on http://${host}:${port}`);
  });
}

// This exposes the RequestHandler
export default createNodeRequestHandler(server);
