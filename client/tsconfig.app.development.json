/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "baseUrl": "./src",
    "outDir": "./out-tsc/app",
    "types": [
      "node"
    ],
    "paths": {
      "@/env/*": ["./environments/*"],
      "@/*": ["./app/*"],
    }
  },
  "files": [
    "src/main.ts"
  ],
  "include": [
    "src/**/*.d.ts",
    "src/**/*.ts"
  ],
  "exclude": [
    "src/main.server.ts",
    "server.ts",
    "server.dev.ts",
    "src/app/app.config.server.ts",
    "src/app/app.routes.server.ts",
    "src/app/app.routes.server.advanced.ts",
    "**/*.server.ts",
    "**/*server*",
    "**/*.spec.ts",
    "src/test.ts"
  ]
}
