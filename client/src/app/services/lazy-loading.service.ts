import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';

export interface LazyLoadConfig {
  rootMargin?: string;
  threshold?: number | number[];
  triggerDistance?: number;
}

export interface VirtualScrollConfig {
  itemHeight: number;
  bufferSize?: number;
  containerHeight?: number;
}

export interface LazyLoadItem {
  id: string;
  element: HTMLElement;
  loaded: boolean;
  loading: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LazyLoadingService {
  private platformId = inject(PLATFORM_ID);
  private observer: IntersectionObserver | null = null;
  private lazyItems = new Map<string, LazyLoadItem>();
  private loadingSubject = new BehaviorSubject<string[]>([]);

  public loading$ = this.loadingSubject.asObservable();

  /**
   * Initialize lazy loading with Intersection Observer
   */
  initLazyLoading(config: LazyLoadConfig = {}): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const defaultConfig: LazyLoadConfig = {
      rootMargin: '50px',
      threshold: 0.1,
      ...config
    };

    this.observer = new IntersectionObserver(
      (entries) => this.handleIntersection(entries),
      {
        rootMargin: defaultConfig.rootMargin,
        threshold: defaultConfig.threshold
      }
    );
  }

  /**
   * Add element to lazy loading observation
   */
  observeElement(id: string, element: HTMLElement): void {
    if (!this.observer || !isPlatformBrowser(this.platformId)) return;

    const item: LazyLoadItem = {
      id,
      element,
      loaded: false,
      loading: false
    };

    this.lazyItems.set(id, item);
    this.observer.observe(element);
  }

  /**
   * Remove element from observation
   */
  unobserveElement(id: string): void {
    if (!this.observer) return;

    const item = this.lazyItems.get(id);
    if (item) {
      this.observer.unobserve(item.element);
      this.lazyItems.delete(id);
    }
  }

  /**
   * Handle intersection observer entries
   */
  private handleIntersection(entries: IntersectionObserverEntry[]): void {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement;
        const item = Array.from(this.lazyItems.values())
          .find(item => item.element === element);

        if (item && !item.loaded && !item.loading) {
          this.loadContent(item);
        }
      }
    });
  }

  /**
   * Load content for a lazy item
   */
  private loadContent(item: LazyLoadItem): void {
    item.loading = true;
    this.updateLoadingState();

    // Simulate content loading (replace with actual loading logic)
    setTimeout(() => {
      item.loaded = true;
      item.loading = false;
      item.element.classList.add('loaded');
      this.updateLoadingState();

      // Unobserve after loading
      if (this.observer) {
        this.observer.unobserve(item.element);
      }
    }, 100);
  }

  /**
   * Update loading state subject
   */
  private updateLoadingState(): void {
    const loadingIds = Array.from(this.lazyItems.values())
      .filter(item => item.loading)
      .map(item => item.id);
    
    this.loadingSubject.next(loadingIds);
  }

  /**
   * Create virtual scroll configuration
   */
  createVirtualScrollConfig(config: VirtualScrollConfig): VirtualScrollConfig {
    return {
      bufferSize: 5,
      containerHeight: 400,
      ...config
    };
  }

  /**
   * Calculate visible items for virtual scrolling
   */
  calculateVisibleItems(
    scrollTop: number,
    containerHeight: number,
    itemHeight: number,
    totalItems: number,
    bufferSize: number = 5
  ): { startIndex: number; endIndex: number; offsetY: number } {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const endIndex = Math.min(totalItems - 1, startIndex + visibleCount + bufferSize * 2);
    const offsetY = startIndex * itemHeight;

    return { startIndex, endIndex, offsetY };
  }

  /**
   * Preload content chunks
   */
  preloadContent(chunks: string[]): Observable<string[]> {
    return new Observable(observer => {
      if (!isPlatformBrowser(this.platformId)) {
        observer.next([]);
        observer.complete();
        return;
      }

      const loadedChunks: string[] = [];
      let loadedCount = 0;

      chunks.forEach((chunk, index) => {
        // Simulate chunk loading with delay
        setTimeout(() => {
          loadedChunks.push(chunk);
          loadedCount++;

          if (loadedCount === chunks.length) {
            observer.next(loadedChunks);
            observer.complete();
          }
        }, index * 50); // Stagger loading
      });
    });
  }

  /**
   * Implement progressive content loading
   */
  loadContentProgressively(
    content: string,
    chunkSize: number = 1000
  ): { chunks: string[]; totalChunks: number } {
    if (!content) return { chunks: [], totalChunks: 0 };

    const chunks: string[] = [];
    let currentIndex = 0;

    while (currentIndex < content.length) {
      const chunk = content.substring(currentIndex, currentIndex + chunkSize);
      chunks.push(chunk);
      currentIndex += chunkSize;
    }

    return { chunks, totalChunks: chunks.length };
  }

  /**
   * Optimize images for lazy loading
   */
  optimizeImages(container: HTMLElement): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const images = container.querySelectorAll('img[data-src]');
    
    images.forEach((img, index) => {
      const imageElement = img as HTMLImageElement;
      const dataSrc = imageElement.getAttribute('data-src');
      
      if (dataSrc) {
        this.observeElement(`img-${index}`, imageElement);
        
        // Replace src when element becomes visible
        imageElement.addEventListener('load', () => {
          imageElement.classList.add('loaded');
        });
      }
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    
    this.lazyItems.clear();
    this.loadingSubject.complete();
  }

  /**
   * Check if element is in viewport
   */
  isInViewport(element: HTMLElement, threshold: number = 0): boolean {
    if (!isPlatformBrowser(this.platformId)) return false;

    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;

    return (
      rect.top >= -threshold &&
      rect.left >= -threshold &&
      rect.bottom <= windowHeight + threshold &&
      rect.right <= windowWidth + threshold
    );
  }

  /**
   * Debounce function for scroll events
   */
  debounce<T extends (...args: any[]) => void>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  /**
   * Throttle function for high-frequency events
   */
  throttle<T extends (...args: any[]) => void>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}
