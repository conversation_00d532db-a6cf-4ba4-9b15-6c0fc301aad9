import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { isPlatformBrowser } from '@angular/common';

export interface StructuredDataArticle {
  '@context': string;
  '@type': string;
  headline: string;
  description: string;
  author: {
    '@type': string;
    name: string;
  };
  publisher: {
    '@type': string;
    name: string;
    logo: {
      '@type': string;
      url: string;
    };
  };
  datePublished: string;
  dateModified: string;
  mainEntityOfPage: {
    '@type': string;
    '@id': string;
  };
  image?: string;
  wordCount?: number;
  articleBody?: string;
}

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  author?: string;
  publishedDate?: string;
  modifiedDate?: string;
  image?: string;
  url?: string;
  type?: 'article' | 'book' | 'library';
  wordCount?: number;
  readingTime?: number;
}

@Injectable({
  providedIn: 'root'
})
export class SeoService {
  private meta = inject(Meta);
  private title = inject(Title);
  private platformId = inject(PLATFORM_ID);

  /**
   * Set comprehensive SEO meta tags for content
   */
  setSEOData(data: SEOData): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Set title
    this.title.setTitle(data.title);

    // Basic meta tags
    this.meta.updateTag({ name: 'description', content: data.description });
    this.meta.updateTag({ name: 'author', content: data.author || 'Advayta.org' });
    
    if (data.keywords) {
      this.meta.updateTag({ name: 'keywords', content: data.keywords });
    }

    // Open Graph tags
    this.meta.updateTag({ property: 'og:title', content: data.title });
    this.meta.updateTag({ property: 'og:description', content: data.description });
    this.meta.updateTag({ property: 'og:type', content: data.type === 'article' ? 'article' : 'website' });
    
    if (data.url) {
      this.meta.updateTag({ property: 'og:url', content: data.url });
    }
    
    if (data.image) {
      this.meta.updateTag({ property: 'og:image', content: data.image });
      this.meta.updateTag({ property: 'og:image:alt', content: data.title });
    }

    // Twitter Card tags
    this.meta.updateTag({ name: 'twitter:card', content: 'summary_large_image' });
    this.meta.updateTag({ name: 'twitter:title', content: data.title });
    this.meta.updateTag({ name: 'twitter:description', content: data.description });
    
    if (data.image) {
      this.meta.updateTag({ name: 'twitter:image', content: data.image });
    }

    // Article-specific meta tags
    if (data.type === 'article' && data.publishedDate) {
      this.meta.updateTag({ property: 'article:published_time', content: data.publishedDate });
      
      if (data.modifiedDate) {
        this.meta.updateTag({ property: 'article:modified_time', content: data.modifiedDate });
      }
      
      if (data.author) {
        this.meta.updateTag({ property: 'article:author', content: data.author });
      }
    }

    // Reading time and word count
    if (data.readingTime) {
      this.meta.updateTag({ name: 'reading-time', content: data.readingTime.toString() });
    }
    
    if (data.wordCount) {
      this.meta.updateTag({ name: 'word-count', content: data.wordCount.toString() });
    }
  }

  /**
   * Generate and inject structured data (JSON-LD) for articles
   */
  setStructuredData(data: SEOData, content?: string): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const structuredData: StructuredDataArticle = {
      '@context': 'https://schema.org',
      '@type': data.type === 'book' ? 'Book' : 'Article',
      headline: data.title,
      description: data.description,
      author: {
        '@type': 'Person',
        name: data.author || 'Advayta.org'
      },
      publisher: {
        '@type': 'Organization',
        name: 'Advayta.org',
        logo: {
          '@type': 'ImageObject',
          url: 'https://advayta.org/assets/logo.png'
        }
      },
      datePublished: data.publishedDate || new Date().toISOString(),
      dateModified: data.modifiedDate || new Date().toISOString(),
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': data.url || window.location.href
      }
    };

    if (data.image) {
      structuredData.image = data.image;
    }

    if (data.wordCount) {
      structuredData.wordCount = data.wordCount;
    }

    if (content) {
      // Clean HTML content for structured data
      const cleanContent = this.cleanHtmlContent(content);
      structuredData.articleBody = cleanContent.substring(0, 5000); // Limit length
    }

    this.injectStructuredData(structuredData);
  }

  /**
   * Calculate reading time based on word count
   */
  calculateReadingTime(wordCount: number): number {
    const wordsPerMinute = 200; // Average reading speed
    return Math.ceil(wordCount / wordsPerMinute);
  }

  /**
   * Count words in HTML content
   */
  countWords(htmlContent: string): number {
    const cleanText = this.cleanHtmlContent(htmlContent);
    return cleanText.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Extract plain text from HTML content
   */
  private cleanHtmlContent(html: string): string {
    if (!isPlatformBrowser(this.platformId)) return '';
    
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  }

  /**
   * Inject structured data script into document head
   */
  private injectStructuredData(data: StructuredDataArticle): void {
    // Remove existing structured data
    const existingScript = document.getElementById('structured-data');
    if (existingScript) {
      existingScript.remove();
    }

    // Create new structured data script
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.id = 'structured-data';
    script.textContent = JSON.stringify(data);
    
    document.head.appendChild(script);
  }

  /**
   * Remove all SEO meta tags (useful for cleanup)
   */
  clearSEOData(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const tagsToRemove = [
      'description', 'keywords', 'author', 'reading-time', 'word-count',
      'og:title', 'og:description', 'og:type', 'og:url', 'og:image', 'og:image:alt',
      'twitter:card', 'twitter:title', 'twitter:description', 'twitter:image',
      'article:published_time', 'article:modified_time', 'article:author'
    ];

    tagsToRemove.forEach(tag => {
      if (tag.startsWith('og:') || tag.startsWith('article:')) {
        this.meta.removeTag(`property="${tag}"`);
      } else {
        this.meta.removeTag(`name="${tag}"`);
      }
    });

    // Remove structured data
    const structuredDataScript = document.getElementById('structured-data');
    if (structuredDataScript) {
      structuredDataScript.remove();
    }
  }
}
