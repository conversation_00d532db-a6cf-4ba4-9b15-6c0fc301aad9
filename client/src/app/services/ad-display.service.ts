import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { AdvertisingService } from './advertising.service';

export interface Advertisement {
  id: number;
  title: string;
  description: string;
  link: string;
  freq: number;
  active: boolean;
  advertising: boolean;
  image?: {
    name: string;
    path: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class AdDisplayService {
  private platformId = inject(PLATFORM_ID);
  private advertisingService = inject(AdvertisingService);
  
  private advertisements: Advertisement[] = [];
  private adTimerId: any = null;
  private adIntervalId: any = null;
  private destroy$ = new Subject<void>();
  private readonly STORAGE_KEY = 'adShownToday';
  private currentAdIndex = 0; // Индекс текущей рекламы для поочередного показа
  
  // Observable for components to listen to ad display events
  private showAdSubject = new BehaviorSubject<Advertisement | null>(null);
  public showAd$ = this.showAdSubject.asObservable();

  // Subject for modal close events
  private modalClosedSubject = new Subject<void>();
  public modalClosed$ = this.modalClosedSubject.asObservable();

  private isInitialized = false;
  private isWaitingForModalClose = false;

  constructor() {
    // Auto-initialize when service is created
    this.initialize();

    // Listen for modal close events to schedule next ad
    this.modalClosed$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        console.log('AdDisplayService: Modal closed event received');
        if (this.isWaitingForModalClose) {
          console.log('AdDisplayService: Scheduling next ad after modal close');
          this.isWaitingForModalClose = false;
          this.scheduleNextAd();
        }
      });
  }

  /**
   * Initialize the ad display system
   */
  private initialize(): void {
    if (!isPlatformBrowser(this.platformId) || this.isInitialized) {
      return;
    }

    this.isInitialized = true;
    this.loadAdvertisements();
  }

  /**
   * Load advertisements from the API
   */
  private loadAdvertisements(): void {
    this.advertisingService.getAll()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          console.log('Loaded advertisements:', res);
          this.advertisements = res.filter((ad: any) => ad.advertising === true);

          // Reset current ad index when loading new advertisements
          this.currentAdIndex = 0;

          // Start ad display after loading
          this.startAdDisplay();
        },
        error: (err) => {
          console.error('Error loading advertisements:', err);
        }
      });
  }

  /**
   * Start the ad display system with initial delay and intervals
   */
  private startAdDisplay(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Clear any existing timers
    this.clearAdTimers();

    // Show first ad after 20 seconds
    this.adTimerId = setTimeout(() => {
      this.showNextAd();
    }, 20000); // 20 seconds
  }

  /**
   * Schedule the next ad after modal is closed
   */
  private scheduleNextAd(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Clear any existing timer
    if (this.adTimerId) {
      clearTimeout(this.adTimerId);
      this.adTimerId = null;
    }

    console.log('AdDisplayService: Scheduling next ad in 30 seconds');
    // Schedule next ad in 30 seconds
    this.adTimerId = setTimeout(() => {
      this.showNextAd();
    }, 30000); // 30 seconds
  }

  /**
   * Show the next available advertisement
   */
  private showNextAd(): void {
    if (!this.advertisements || this.advertisements.length === 0) {
      return;
    }

    // Find ads that haven't reached their daily frequency limit
    const availableAds = this.advertisements.filter(ad => {
      const todayShownCount = this.getTodayShownCount(ad.id);
      return todayShownCount < ad.freq;
    });

    if (availableAds.length === 0) {
      // All ads have reached their daily frequency limit
      this.clearAdTimers();
      console.log('All ads have reached their daily frequency limit');
      return;
    }

    // Select ad by round-robin (поочередно)
    let selectedAd: Advertisement | null = null;
    let attempts = 0;

    // Попытаемся найти следующую доступную рекламу, начиная с текущего индекса
    while (attempts < this.advertisements.length && !selectedAd) {
      const candidateAd = this.advertisements[this.currentAdIndex];
      const todayShownCount = this.getTodayShownCount(candidateAd.id);

      if (todayShownCount < candidateAd.freq) {
        selectedAd = candidateAd;
      } else {
        // Переходим к следующей рекламе
        this.currentAdIndex = (this.currentAdIndex + 1) % this.advertisements.length;
        attempts++;
      }
    }

    if (!selectedAd) {
      // Все рекламы достигли дневного лимита
      this.clearAdTimers();
      console.log('All ads have reached their daily frequency limit');
      return;
    }

    // Переходим к следующей рекламе для следующего показа
    this.currentAdIndex = (this.currentAdIndex + 1) % this.advertisements.length;

    // Increment today's shown count for this ad
    this.incrementTodayShownCount(selectedAd.id);
    const newTodayCount = this.getTodayShownCount(selectedAd.id);

    console.log(`Showing ad ${selectedAd.id}: ${newTodayCount}/${selectedAd.freq} times today (round-robin)`);

    // Set flag to wait for modal close before scheduling next ad
    this.isWaitingForModalClose = true;

    // Emit the ad to be displayed
    this.showAdSubject.next(selectedAd);

    // Check if we should stop after this ad
    if (newTodayCount >= selectedAd.freq) {
      console.log(`Ad ${selectedAd.id} has reached its daily frequency limit of ${selectedAd.freq}`);
    }

    // If all ads have reached their daily limit, stop the timers
    const allAdsReachedDailyLimit = this.advertisements.every(ad =>
      this.getTodayShownCount(ad.id) >= ad.freq
    );

    if (allAdsReachedDailyLimit) {
      console.log('All ads have reached their daily frequency limits, stopping ad display');
      this.clearAdTimers();
    }
  }

  /**
   * Stop the ad display system
   */
  public stopAdDisplay(): void {
    this.clearAdTimers();
  }

  /**
   * Restart the ad display system
   */
  public restartAdDisplay(): void {
    this.clearAdTimers();
    this.loadAdvertisements();
  }

  /**
   * Clear all ad timers
   */
  private clearAdTimers(): void {
    if (this.adTimerId) {
      clearTimeout(this.adTimerId);
      this.adTimerId = null;
    }
    if (this.adIntervalId) {
      clearInterval(this.adIntervalId);
      this.adIntervalId = null;
    }
  }

  /**
   * Get current advertisements
   */
  public getAdvertisements(): Advertisement[] {
    return this.advertisements;
  }

  /**
   * Get today's ad shown counts
   */
  public getTodayAdShownCounts(): { [adId: number]: number } {
    const dailyData = this.getDailyAdCounts();
    return dailyData.counts;
  }

  /**
   * Notify that the modal has been closed
   */
  public notifyModalClosed(): void {
    this.modalClosedSubject.next();
  }

  /**
   * Get today's date in YYYY-MM-DD format
   */
  private getTodayString(): string {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * Get daily ad counts from localStorage
   */
  private getDailyAdCounts(): { date: string; counts: { [adId: number]: number } } {
    if (!isPlatformBrowser(this.platformId)) {
      return { date: this.getTodayString(), counts: {} };
    }

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        // Check if the stored date is today
        if (data.date === this.getTodayString()) {
          return data;
        }
      }
    } catch (error) {
      console.error('Error reading daily ad counts from localStorage:', error);
    }

    // Return fresh data for today
    return { date: this.getTodayString(), counts: {} };
  }

  /**
   * Save daily ad counts to localStorage
   */
  private saveDailyAdCounts(counts: { [adId: number]: number }): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    try {
      const data = {
        date: this.getTodayString(),
        counts: counts
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving daily ad counts to localStorage:', error);
    }
  }

  /**
   * Get today's shown count for a specific ad
   */
  private getTodayShownCount(adId: number): number {
    const dailyData = this.getDailyAdCounts();
    return dailyData.counts[adId] || 0;
  }

  /**
   * Increment today's shown count for a specific ad
   */
  private incrementTodayShownCount(adId: number): void {
    const dailyData = this.getDailyAdCounts();
    dailyData.counts[adId] = (dailyData.counts[adId] || 0) + 1;
    this.saveDailyAdCounts(dailyData.counts);
  }

  /**
   * Cleanup when service is destroyed
   */
  public ngOnDestroy(): void {
    this.clearAdTimers();
    this.destroy$.next();
    this.destroy$.complete();
  }
}
