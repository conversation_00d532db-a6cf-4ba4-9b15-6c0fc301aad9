/**
 * Utility function for Russian pluralization
 * @param count - The number to determine pluralization for
 * @param forms - Array of three forms: [singular, few, many] (e.g., ['лекция', 'лекции', 'лекций'])
 * @returns The correct pluralized form
 */
export function pluralizeRussian(count: number, forms: [string, string, string]): string {
  const [singular, few, many] = forms;
  
  // Handle special cases for numbers ending in 11-14
  if (count % 100 >= 11 && count % 100 <= 14) {
    return many;
  }
  
  const lastDigit = count % 10;
  
  if (lastDigit === 1) {
    return singular;
  } else if (lastDigit >= 2 && lastDigit <= 4) {
    return few;
  } else {
    return many;
  }
}

/**
 * Content type definitions for pluralization
 */
export type ContentType = 'lectures' | 'articles' | 'books';

/**
 * Content type pluralization forms
 */
const CONTENT_TYPE_FORMS: Record<ContentType, [string, string, string]> = {
  lectures: ['лекция', 'лекции', 'лекций'],
  articles: ['статья', 'статьи', 'статей'],
  books: ['книга', 'книги', 'книг']
};

/**
 * Formats the content count message with proper Russian pluralization
 * @param count - Number of items found
 * @param contentType - Type of content (lectures, articles, books)
 * @returns Formatted message string
 */
export function formatContentCountMessage(count: number, contentType: ContentType): string {
  const forms = CONTENT_TYPE_FORMS[contentType];
  const pluralizedForm = pluralizeRussian(count, forms);
  return `Найдено ${count} ${pluralizedForm}`;
}

/**
 * @deprecated Use formatContentCountMessage instead
 * Formats the lecture count message with proper Russian pluralization
 * @param count - Number of lectures found
 * @returns Formatted message string
 */
export function formatLectureCountMessage(count: number): string {
  return formatContentCountMessage(count, 'lectures');
}
