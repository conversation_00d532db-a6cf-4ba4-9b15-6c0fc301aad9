import { ContentService } from '@/services/content.service'
import { Injectable, inject } from '@angular/core'
import { ActivatedRouteSnapshot, Resolve } from '@angular/router'
import { Observable, of } from 'rxjs'
import { catchError, map } from 'rxjs/operators'

@Injectable({
  providedIn: 'root'
})
export class ContentTitleResolver implements Resolve<string> {
  contentService = inject(ContentService)

  resolve(route: ActivatedRouteSnapshot): Observable<string> {
    const page = route.paramMap.get('page');
    const lang = route.paramMap.get('lang') || 'ru';
    
    if (!page) return of('Статья');
    
    return this.contentService.getContentPreview(lang, page).pipe(
      map((content: any) => content.title || 'Статья'),
      catchError(() => of('Статья'))
    );
  }
}
