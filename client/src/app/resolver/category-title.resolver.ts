import { Injectable, inject } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ContentService } from '@/services/content.service';

@Injectable({
  providedIn: 'root'
})
export class CategoryTitleResolver implements Resolve<string> {
  contentService = inject(ContentService)

  resolve(route: ActivatedRouteSnapshot): Observable<string> {
    const categoryId = route.paramMap.get('id');
    
    if (!categoryId) return of('Категория');

    return this.contentService.getCategories().pipe(
      map((categories: any) => {
        const category = categories.find((c: any) => c.id == categoryId);
        return category?.title || 'Категория';
      }),
      catchError(() => of('Категория'))
    );
  }
}
