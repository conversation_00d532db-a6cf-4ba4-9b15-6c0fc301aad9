import { ForumService } from "@/services/forum.service"
import { Injectable, inject } from '@angular/core'
import { ActivatedRouteSnapshot, Resolve } from '@angular/router'
import { of } from 'rxjs'
import { map } from 'rxjs/operators'

@Injectable({
  providedIn: 'root'
})
export class ForumCategoryTitleResolver implements Resolve<string> {
  forumService = inject(ForumService)

  resolve(route: ActivatedRouteSnapshot) {
    const categoryId = route.paramMap.get('id');

    if (!categoryId) return of('Категория');

    return this.forumService.getCategories().pipe(
      map((categories: any) => {
        const category = categories.items.find((c: any) => c.id == categoryId);
        return category?.name || 'Категория';
      })
    );
  }
}
