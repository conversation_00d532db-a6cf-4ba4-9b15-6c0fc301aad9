import { ForumService } from "@/services/forum.service"
import { Injectable, inject } from '@angular/core'
import { ActivatedRouteSnapshot, Resolve } from '@angular/router'
import { of } from 'rxjs'
import { catchError, map } from 'rxjs/operators'

export interface ForumTopicBreadcrumb {
  categoryName: string;
  categoryId: number;
  topicName: string;
}

@Injectable({
  providedIn: 'root'
})
export class ForumTopicTitleResolver implements Resolve<string> {
  forumService = inject(ForumService)

  resolve(route: ActivatedRouteSnapshot) {
    const topicId = route.paramMap.get('id');

    if (!topicId) return of('Тема');

    return this.forumService.getTopic(topicId).pipe(
      map((topic: any) => {
        if (topic?.category) {
          return {
            categoryName: topic.category.name,
            categoryId: topic.category.id,
            topicName: topic.name || 'Тема'
          };
        }
        return topic?.name || 'Тема';
      }),
      catchError(() => of('Тема'))
    );
  }
}
