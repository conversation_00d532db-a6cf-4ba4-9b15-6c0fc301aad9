import { CarouselComponent } from '@/components/carousel/carousel.component'
import { CarouselContent } from '@/interfaces/carouselItemContent'
import { ConstructorService } from "@/services/constructor.service"
import { ContentService } from '@/services/content.service'
import { LibraryService } from "@/services/library.service"
import { ShareDataService } from '@/services/share-data.service'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { Component, DestroyRef, Inject, inject, PLATFORM_ID } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { Meta, Title } from "@angular/platform-browser"
import { ActivatedRoute, Router } from '@angular/router'
import { TranslocoService } from "@jsverse/transloco"

@Component({
  selector: 'app-main',
  standalone: true,
  imports: [CommonModule, CarouselComponent, NgOptimizedImage],
  templateUrl: './main.component.html',
  styleUrl: './main.component.scss'

})
export class MainComponent {
  constructorService = inject(ConstructorService)
  titleService = inject(Title)
  metaService = inject(Meta)
  translocoService = inject(TranslocoService)
  contentService = inject(ContentService)
  libraryService = inject(LibraryService)
  route = inject(ActivatedRoute)
  router = inject(Router);
  slidersConstructorData!: CarouselContent | any;
  private readonly destroyRef = inject(DestroyRef);

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private shareDataService: ShareDataService
  ) { }

  quickLinks = [
    { icon: 'assets/images/flower.webp', label: 'Аудиолекции', decoration: false, link: 'audio' },
    { decoration: true },
    { icon: 'assets/images/book.webp', label: 'Священные тексты', decoration: false, link: 'library' },
    { decoration: true },
    { icon: 'assets/images/notes.webp', label: 'Бхаджаны', decoration: false, link: 'audio' },
    { decoration: true },
    { icon: 'assets/images/play.webp', label: 'Видеосатсанги', decoration: false, link: '' },
    { decoration: true },
    { icon: 'assets/images/shell.webp', label: 'Радио Дхарма', decoration: false, link: '' }
  ];
  quickLinks2 = [
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Приехать в Ашрам'
    },
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Стать учеником'
    },
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Стать санньяси'
    },
    {
      content: 'Место для вашего текста. Место для текста.', label: 'Создать Дхарма центр'
    },
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Создать ашрам'
    }
  ];

  quickLinks2mid1 = [
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Приехать в Ашрам'
    },
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Получить духовное имя'
    },
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Стать учеником'
    },
  ]
  quickLinks2mid2 = [
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Стать санньяси'
    },
    {
      content: 'Место для вашего текста. Место для текста.', label: 'Создать Дхарма центр'
    },
    {
      content: 'Место для вашего текста. Место для текста. Место для вашего текста.Место для текста.', label: 'Создать ашрам'
    }
  ];

  quickLinks4 = [
    {
      label: 'Приехать в ашрам', text: "Место для вашего текста. Место для текста. Место для вашего текста.Место для текста."
    },
    {
      label: 'Получить духовное имя', text: "Место для вашего текста. Место для текста. Место для вашего текста.Место для текста."
    },
    {
      label: 'Стать санньяси', text: "Место для вашего текста. Место для текста. Место для вашего текста.Место для текста."
    },
    {
      label: 'Стать санньяси', text: null
    }
  ];

  quickLinks3 = [
    { label: 'Консультация с монахом', text: "Место для вашего текста. Место для текста." },
    { label: 'Заказать ритуал', text: "Место для вашего текста. Место для текста." },
    { label: 'Заказать хому', text: "Место для вашего текста. Место для текста." }
  ];

  ngOnInit() {
    this.constructorService.getCarousels().pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (data) => {
        this.slidersConstructorData = data;
      }, error: (err) => {
        console.error('Error fetching data:', err);
      },
    });
    this.titleService.setTitle('Главная страница | Advayta.org1');
    this.metaService.updateTag({ name: 'description', content: 'Описание главной страницы advayta.org' })
    this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
      this.translocoService.setActiveLang(params['lang'])
      // this.contentService.getAll().subscribe()
      // this.libraryService.getAll().subscribe()
    })
  }

  navigateTo(label: string | undefined) {
    switch (label) {
      case 'Аудиолекции':
        this.navigateToAudio();
        break;
      case 'Священные тексты':
        this.navigateToLibrary();
        break;
      case 'Бхаджаны':
        this.navigateToAudio();
        break;
      case 'Видеосатсанги':
        this.navigateToVideo();
        break;
      case 'Радио Дхарма':
        this.playRadio('https://c36.radioboss.fm:8047/stream');
        break;
      default:
        break;
    }
  }

  playRadio(link: string) {
    this.shareDataService.playRadio(link, true);
  }

  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
  }

  navigateToVideo() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/videolektsii`]);
  }
}
