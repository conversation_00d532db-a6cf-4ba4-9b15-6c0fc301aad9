import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LibraryComponent } from './library.component';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';

describe('LibraryComponent - Text Width Feature', () => {
  let component: LibraryComponent;
  let fixture: ComponentFixture<LibraryComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LibraryComponent],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { params: { id: '1' }, queryParams: {} },
            params: of({ id: '1' })
          }
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LibraryComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default text width set to narrow', () => {
    expect(component.fullscreenBookTextWidth).toBe('narrow');
  });

  it('should return correct styles for narrow width', () => {
    component.fullscreenBookTextWidth = 'narrow';
    const styles = component.getFullscreenTextWidthStyles();
    expect(styles['max-width']).toBe('700px');
    expect(styles['margin']).toBe('0 auto');
  });

  it('should return correct styles for medium width', () => {
    component.fullscreenBookTextWidth = 'medium';
    const styles = component.getFullscreenTextWidthStyles();
    expect(styles['max-width']).toBe('930px');
    expect(styles['margin']).toBe('0 auto');
  });

  it('should return correct styles for full width', () => {
    component.fullscreenBookTextWidth = 'full';
    const styles = component.getFullscreenTextWidthStyles();
    expect(styles['max-width']).toBe('100%');
    expect(styles['margin']).toBe('0 auto');
  });

  it('should change text width when option is clicked', () => {
    component.fullscreenBookTextWidth = 'narrow';
    
    // Simulate clicking medium option
    component.fullscreenBookTextWidth = 'medium';
    expect(component.fullscreenBookTextWidth).toBe('medium');
    
    // Simulate clicking full option
    component.fullscreenBookTextWidth = 'full';
    expect(component.fullscreenBookTextWidth).toBe('full');
  });
});
