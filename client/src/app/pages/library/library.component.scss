@import url(../categories/category/category.component.scss);

.library-tabs {
  display: flex;
  justify-content: center;
}

.library-tabs::after {
  content: '';
  background-image: var(--lib-after);
  width: 100%;
  height: 3px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
}

.book_text.show-full-text {
  -webkit-line-clamp: unset !important;
  display: block !important;
}

.library-tab.is-active {
  background: var(--tab_active);
  color: rgba(255, 255, 255, 0.9529);
  z-index: 2;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.library-tab:not(.is-active) {
  cursor: pointer;
}

.library-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 350px;
  height: 61px;
  background: var(--tab_nominal);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin-right: -60px;
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 20px;
  color: var(--text-color);
  cursor: pointer;
}

.library-tab:last-child {
  margin-right: 0;
  z-index: unset;

  &.is-active {
    z-index: 2;
  }
}

.library-tab:nth-child(2) {
  z-index: 0;
}

.wrapper_line.custom_ {
  padding: 120px 0 0 0;
}

.wrapper_line.custom_l {
  padding: 0 0 167px 0;
}

select {
  max-width: 100%;
}

.w-one {
  select {
    width: 140px;
    margin-right: 10px;
  }
}

.book_top {
  display: flex;
  justify-content: space-between;

  .img_book {
    width: 270px;
    img {
      height: 360px;
    }
  }

  .book_text.underline {
    margin-top: 20px;
  }

  .book_text:not(.underline):not(.scnd):first-letter {
    font-family: BeaumarchaisC;
    font-weight: 400;
    font-size: 90px;
    line-height: 90px;
    text-align: justify;
    color: var(--book_about);
  }

  .book_text {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 34px;
    color: var(--font-color1);
    overflow: hidden;
    // display: -webkit-box;
    // -webkit-line-clamp: 4;
    // -webkit-box-orient: vertical;
  }

  .author_t {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 34px;
    color: var(--text-color);
    margin-top: 45px;
  }

  .book_about {
    width: 65%;
  }

  .btn_book {
    position: relative;
    z-index: 11;
    background: var(--buttn_catg2);
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    width: 270px;
    height: 50px;
    color: rgb(255, 255, 255);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30px 0 0 0;
  }
}

.book_chips {
  display: flex;
  margin: 30px 0 0 0;
  flex-wrap: wrap;
  gap: 13px;
}

.book_chip {
  display: flex;
  align-items: center;
  width: fit-content;
  font-family: Prata;
  font-weight: 400;
  line-height: 24px;
  color: var(--font-color1);
  border: 1px solid var(--book_about);
  cursor: pointer;
  transition: all 0.2s;
  height: 40px;
  font-size: 17px;
  padding: 0 20px;
  border-radius: 10px;
  &:hover {
    background-color: var(--book_about);
    color: #fff;
  }
}

.library-page {
  padding: 0 0 50px 0px;
  .icons_w {
    margin: 0;
  }
}

.library-author {
  color: gray;
  font-size: 14px;
}

.library-content {
  padding: 15px 0px
}

.library-content select {
  padding: 10px;
}

.arrow {
  position: absolute;
  top: 50%;
  margin-top: -32px;
  font-size: 64px;
  color: #E2E2E2;
  font-family: arial, sans-serif;
  font-weight: bold;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  text-decoration: none;
}

.arrow:hover,
.navlink:hover {
  color: #777;
}

.arrow:active,
.navlink:hover {
  color: #000;
}

#prev {
  left: 0;
}

#next {
  right: 0;
}

.library-book {
  position: relative;
}

// .library-header {
//   padding: 20px 0px;
// }

.library-image img {
  max-width: 200px;
}

.library-about {
  display: flex;
}

.library-props {
  padding-left: 20px;
}

.library-prop {
  margin-bottom: 15px;
}

.library-description {
  margin-top: 20px;
}

.library-description__content {
  padding: 20px;
  line-height: 30px;
}

.btn-like {
  svg {
    width: 24px;
    height: 24px;
  }
}

.likes-count {
  min-width: 12px;
}

.btn-favourite svg {
  width: 25px;
  height: 24px;
}

.library-item {
  background-image: linear-gradient(360deg, #f2ead4 4.3%, #d6c28d);
}

// .audio-views img {
//   width: 20px;
//   height: 20px;
// }




#epub {
  background: white;
  padding: 20px;
}


.light {
  background: #fff !important;
  color: black;
}

.dark {
  background: #000 !important;
  color: white;
}

.tabs_w {
  margin-top: -60px;
}

.library_header_action {
  height: 50px;
  margin: 45px 0;
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 18px;
  letter-spacing: 0;
  color: var(--font-color1);
  .action_header_wrapper {
    gap: 30px;
  }
}

.add-to-queue-btn {
  @extend .book_chip;
  cursor: pointer;
  margin: 0;
  display: flex;
  justify-content: center;
}

.book_text_section {
  position: sticky;
}

.conts_list {
  max-height: 440px;
  overflow-y: auto;
}

.audio-section {
  flex: 1 1 0;
  margin-left: 60px;
  font-family: Prata;
  font-weight: 400;
  letter-spacing: 0;
  color: var(--font-color1);

  .library-audio__list {
    border-radius: 15px;
    border: 1px solid var(--book_about);
    padding: 14px 0;
    .library-audio__list-inner {
      max-height: 238px;
      overflow-y: auto;
      .library-audio {
        padding: 14px 28px;
        font-size: 20px;
        line-height: 20px;
        &.active-audio {
          background-color: var(--selection);
        }
        .audio-name {
          flex: 1 1 0;
        }
        .audio-time {
          font-size: 16px;
          line-height: 16px;
          text-align: right;
        }
        .library-play {
          margin-right: 4px;
          svg {
            path,
            rect {
              fill: var(--font-color1);
            }
          }
        }
      }
    }
  }
  .audio-section-footer {
    padding-top: 66px;
    .reader-name {
      font-size: 24px;
      line-height: 34px;
    }
  }
}
.all-time {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 18px;
  color: var(--font-color1);
  svg {
    path {
      fill: var(--font-color1);
    }
  }
}
.book-text-header {
  padding: 57px 0 45px;
  .full-screen-btn {
    @extend .book_chip;
    cursor: pointer;
    margin: 0;
    &:hover {
      svg {
        path {
          fill: #fff;
        }
      }
    }
  }
  .book-content-section {
    gap: 30px;
  }
  .conts_title {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 24px;
    color: var(--font-color1);
    padding: 0 12px 12px 26px;
  }

    .conts_item {
      display: flex;
      align-items: center;
      font-family: Prata;
      font-weight: 400;
      font-size: 18px;
      line-height: 1.4;
      color: var(--font-color1);
      height: 44px;
      padding: 0 12px 0 26px;
      min-height: 44px;
      height: fit-content;
  
      &:hover {
        background-color: var(--selection);
      }
  
      &.active {
        background-color: var(--book_about);
        color: white;
      }
    }
}



// Универсальные стили для активного элемента содержания в fullscreen режиме
.fullscreen-book-dialog {
  .conts_item.active {
    background-color: var(--book_about) !important;
    color: white !important;
  }

  // Стили для светлой темы
  &.light {
    .settings-container {
      .settings-options {
        background-color: rgba(255, 251, 242, 1) !important;
        border: 1px solid var(--text-color) !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
        z-index: 99999 !important;
        position: absolute !important;
        top: calc(100% + 5px) !important;
        right: 0 !important;

        .theme-option,
        .font-option,
        .width-option {
          color: var(--text-color) !important;
          background-color: transparent !important;
          pointer-events: auto !important;
          cursor: pointer !important;

          &.active-font {
            color: var(--book_about) !important;
            font-weight: bold !important;
          }

          &.active-theme {
            color: var(--book_about) !important;
            font-weight: bold !important;
          }

          &.active-width {
            color: var(--book_about) !important;
            font-weight: bold !important;
          }
        }

        input[type="range"] {
          pointer-events: auto !important;
        }
      }
    }
  }
}


.book-views {
  width: 270px;
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 18px;
  letter-spacing: 0;
  text-align: center;
  color: var(--font-color1);
  margin-top: 45px;
  margin-right: 60px;
}

.lotus_divider {
  width: 100%;
  height: 28px;
  margin: 70px 0;
}

.related_books_section {
  display: flex;
  flex-direction: column;
  gap: 70px;
  color: var(--font-color1);
  font-family: Prata;
  font-weight: 400;
  letter-spacing: 0;
  .related_books_title {
    font-size: 24px;
    line-height: 24px;
  }
  .related_books_container {
    display: flex;
    justify-content: space-between;
    .related_book {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 210px;
      .img_book {
        width: 210px;
        height: 280px;
        position: relative;
        img {
            height: 280px;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            object-fit: cover;
          }
      }
      &_name {
        font-size: 20px;
        line-height: 28px;
        padding-top: 17px;
      }
      &_author {
        font-size: 20px;
        line-height: 28px;
        color: var(--text-color);
      }
    }
  }
}

.desktop-button-buy {
  display: block;
}

.mobile-button-buy {
  display: none;
}

.mobile-related-books-buttons {
  display: none;
}

.show-last-book {
  justify-content: flex-start;
}

.next_btn, .prev_btn {
  position: relative;
  z-index: 11;
  background: var(--buttn_catg2);
  cursor: pointer;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  width: 270px;
  height: 50px;
  color: rgb(255, 255, 255);
  display: flex;
  align-items: center;
  justify-content: center;
  &.disabled {
    opacity: 0.7;
    pointer-events: none;
  }
}

.mobile-add-queue-btn {
  display: none;
}

.book-text-nav {
  margin-top: 40px;
  align-items: center;

  .page-indicator {
    font-family: Prata;
    font-size: 16px;
    color: var(--font-color1);
    margin: 0 20px;
    white-space: nowrap;
  }
}

.desktop-book_chips {
  display: flex;
}
.mobile-book_chips {
  display: none;
}

@media (max-width: 1250px) {
  .mobile-book_chips {
    display: flex;
    padding: 0 79px;
  }

  .desktop-book_chips {
    display: none;
  }

  .wrapper_line.custom_l {
    padding: 0 0 86px 0;
  }

  .middle_stripe {
    max-width: 768px;
  }

  .book_text_wrapper {
    padding: 0 79px;
  }

  .book_chip {
    font-size: 18px;
    line-height: 18px;
    border-radius: 10px;
    padding: 10px 19px;
    margin: 0;
  }

  .library_header_action {
    height: 39px;
    padding: 0 79px;
  }

  .add-to-queue-btn {
    font-size: 18px;
    height: 39px;
  }

  .book_top {
    gap: 20px;
    padding: 0 79px;
    &.book-buy-and-chips-row {
      flex-direction: column;
      gap: 25px;
    }

    .book_about {
      width: auto;
    }

    .img_book {
      width: 220px;
      min-width: 220px;
      img {
        height: 293px;
      }
    }

    .book_text.underline {
      margin-top: 4px;
    }

    .book_text:not(.underline):not(.scnd):first-letter {
      font-size: 70px;
      line-height: 70px;
    }

    .book_text {
      font-size: 18px;
      line-height: 26px;
    }

    .author_t {
      font-size: 18px;
      line-height: 26px;
      margin-top: 45px;
    }

    .book_about {
      flex: 1 1 0;
    }

    .btn_book {
      font-size: 18px;
      line-height: 20px;
      width: 220px;
      margin: 30px 0 0 0;
      background-size: contain;
    }
    .book_chips {
      // margin: 0;
      gap: 10px;
    }
  }

  .lotus_divider {
    margin: 45px 0;
  }

  .related_books_section {
    gap: 45px;
    padding: 0 79px;
    justify-content: space-between;
    .related_books_title {
      font-size: 24px;
      line-height: 28px;
    }
    .related_books_container {
      gap: 35px;
      .related_book {
        flex: 1 1 0;
        .img_book {
          display: flex;
          align-items: center;
          max-width: 170px;
          height: 215px;
          width: 100%;
          img {
            height: 224px;
          }
        }
        &_name {
          font-size: 18px;
          line-height: 22px;
          padding-top: 20px;
        }
        &_author {
          font-size: 15px;
          line-height: 19px;
        }
      }
    }
  }

  .audio-section {
  margin-left: 0;
  .library-audio__list {
    border-radius: 10px;
    border: 1px solid var(--book_about);
    padding: 12px 0;
    .library-audio__list-inner {
      max-height: 204px;
      .library-audio {
        padding: 12px 20px;
        font-size: 16px;
        line-height: 20px;
        &.active-audio {
          background-color: var(--selection);
        }
        .audio-name {
          flex: 1 1 0;
        }
        .audio-time {
          font-size: 12px;
          line-height: 16px;
          text-align: right;
        }
      }
    }
  }
  .audio-section-footer {
    display: none;
  }
}
.all-time {
  font-size: 12px;
  white-space: nowrap;
}


  .library-tab {
    width: 220px;
    height: 38px;
    margin-right: -37px;
    font-size: 18px;
    line-height: 18px;
    color: var(--text-color);
    cursor: pointer;
  }

  .tabs_w {
    max-width: 768px;
    margin: -38px auto 0;
  }

  .library-tabs {
    max-width: 768px;
  }

  .library-tabs::after {
    content: '';
    background-image: var(--lib-after_md);
    width: 100%;
    height: 2px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
  }

  .desktop-add-queue-btn {
    display: none;
  }

  .mobile-add-queue-btn {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 24px;
  }
}
@media (max-width: 768px) {

  .mobile-book_chips {
    padding: 0 7px;
    margin: 20px 0 0;
  }

  .book_chip {
    font-size: 15px;
    line-height: 15px;
    padding: 10px 15px;
    margin: 0;
  }

  .book_text_wrapper {
    padding: 0 25px;
  }

  .library_header_action {
    height: 36px;
    padding: 0 7px;
    font-size: 12px;
    line-height: 14px;
    margin: 30px 0;
    .action_header_wrapper {
      gap: 15px;
    }
    .icons_w {
      display: flex;
    }
  }

  .book_top {
    gap: 20px;
    padding: 0 7px;
    flex-direction: column;
    &.book-buy-and-chips-row {
      gap: 0;
    }

    .book-img_btn-buy-section {
      display: flex;
      flex-direction: column;
      width: fit-content;
      margin: 0 auto;
      .img_book {
        width: 210px;
        min-width: 210px;
        img {
          height: 280px;
        }
      }
      .btn_book {
        font-size: 17px;
        line-height: 17px;
        width: 210px;
      }
    }

    .book_text:not(.underline):not(.scnd):first-letter {
      font-size: 56px;
      line-height: 56px;
    }

    .book_text {
      font-size: 14px;
      line-height: 22px;
    }

    .author_t {
      font-size: 14px;
      line-height: 22px;
      margin-top: 20px;
    }

    .book_chips {
      margin: 21px 0 0 0;
      gap: 10px;
    }
  }

  .lotus_divider {
    margin: 40px 0;
    height: 17px;
  }

  .related_books_section {
    padding: 0 7px;
    .related_books_container {
      gap: 30px;

      justify-content: space-between;
      overflow-x: scroll;
      overflow-y: hidden;

      .related_book {
        flex: 1 1 0;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        .img_book {
          width: 100%;
          min-width: 151px;
          img {
            -webkit-user-drag: none;
          }
        }
        &_name {
          font-size: 17px;
          line-height: 21px;
          padding-top: 35px;
        }
        &_author {
          font-size: 14px;
        }
      }
    }
  }

  .desktop-button-buy {
    display: none;
  }

  .mobile-button-buy {
    display: block;
  }

  .btn-favourite {
    span {
      display: none;
    }
    svg {
      width: 18px !important;
      height: 17px !important;
    }
  }

  .icons_w.in-favourites .star_w {
    width: 18px !important;
    height: 17px !important;
  }

  .btn_like {
    svg {
      width: 17px;
      height: 16px;
    }
  }

  .icons_w.is-liked .like_w {
    width: 17px !important;
    height: 16px !important;
  }

  .share_icon {
    span {
      display: none;
    }
    svg {
      width: 16px;
      height: 16px;
    }
}

  .book-text-nav {
    flex-direction: column;
    align-items: center;
  }
  .next_btn, .prev_btn {
    height: 40px;
    font-size: 17px;
    line-height: 17px;
  }

  .book-text-header {
    padding: 30px 0;
    .book-content-section {
      .contents-btn{
        display: block;
        margin: 0 !important;
        height: fit-content;
        outline: no;
        .btn-label {
          display: none;
        }
        .content_w {
          width: 16px;
          height: 16px;
        }
        svg {
          width: 16px !important;
          height: 16px !important;
        }
      }
    }
    .full-screen-btn {
      padding: 0;
      border: none;
      &:hover {
        background: transparent;
        svg {
          opacity: 0.7;
          path {
            fill: var(--text-color);
          }
        }
      }
      span {
        display: none;
      }
    }

    .hidden-mobile {
      visibility: hidden !important;
      pointer-events: none !important;
    }

    .icons_w:hover .content_w {
      width: 16px;
      height: 16px;
    }
  }

  .all-time {
    font-size: 14px;
    line-height: 14px;
    svg {
      width: 16px;
      height: 16px;
    }
  }

  .mobile-add-queue-btn {
    margin-bottom: 20px;
  }

  .add-to-queue-btn {
    font-size: 17px;
    line-height: 17px;
    height: 38px;
    width: 100%;
  }
}

@media (max-width: 600px) {

  .book_text_wrapper {
    padding: 0 7px;
  }

  .book-text-header {
    padding: 20px 0;
  }

  .library-tab {
    width: 135px;
    height: 26px;
    flex: 1 1 0;
    margin-right: -16px;
    font-size: 12px;
    line-height: 12px;
    color: var(--text-color);
    cursor: pointer;
  }

  .tabs_w {
    margin: -30px -10px 0;
  }

  .related_books_container {
    max-width: 335px;
    margin: 0 auto;
    justify-content: flex-start;
    &.show-last-book {
      justify-content: flex-end !important;
    }
  }
  .related_books_title {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    .mobile-related-books-buttons {
      display: flex;
      align-items: center;
      gap: 20px;
      .next-button {
        transform: rotate(180deg);
      }
    }
  }

  .book_chip {
    height: 36px;
    font-size: 15px;
    line-height: 18px;
    padding: 0 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s;
  }
}





// Add these styles at the end of the file

.fullscreen-book-dialog {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  margin: 0;
  padding: 0;
  border: none;
  background-color: var(--main-back-gradient);
  color: var(--font-color1);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  &.light {
    &::backdrop {
      background-color: rgba(255, 251, 242, 1);
    }
    .dialog-header {
      background-color: rgba(255, 251, 242, 1);
    }
    .book-pages-container {

      .book-page {

        &.left-page {
          background-color: rgba(255, 251, 242, 1);
        }

        &.right-page {
          background-color: rgba(255, 251, 242, 1);
        }

        // Book content styling
        p, div {
          color: var(--font-color1);
        }

        h1, h2, h3, h4, h5, h6 {
          color: var(--book_about);
        }
      }

    }
    .book-navigation {
      background-color: rgba(255, 251, 242, 1);
    }
  }
  &.dark {
    &::backdrop {
      background-color: #282828;
    }
    .dialog-header {
      background-color: #282828;
      color: var(--border);
      .dialog-title {
        color: var(--border);
      }

      .header-actions {
        .contents-btn {
          .icon-wrap {
            svg {
              rect {
                fill: var(--border);
              }
            }
          }
          .btn-label {
            color: var(--border);
          }
          &:hover {
            .icon-wrap {
              svg {
                rect {
                  fill: var(--light-color);
                }
              }
            }
            .btn-label {
              color: var(--text-color);
            }
          }
        }
      }

      .close-button {
        color: var(--border);
        top: 30px;

        &:hover {
          color: var(--text-color)
        }
      }
      .settings-container{
        .settings-button {
          border: 1px solid var(--text-color);
          color: var(--border);
        }
        .settings-options {
          background-color: #282828 !important;
          color: var(--border) !important;
          border: 1px solid var(--border) !important;
          z-index: 10002 !important;

          .color-themes-options,
          .fonts-options,
          .font-size-slider-container,
          .text-width-options {
            z-index: 10003 !important;
          }

          .theme-option,
          .font-option,
          .width-option {
            z-index: 10004 !important;
            cursor: pointer !important;
            pointer-events: auto !important;
          }

          input[type="range"] {
            z-index: 10004 !important;
            pointer-events: auto !important;
          }

          .fonts-options {
            .font-option {
              &.active-font {
                color: var(--border);
              }
            }
          }

          .text-width-options {
            .width-option {
              &.active-width {
                color: var(--border);
              }
            }
          }
        }

      }


    }
    .book-pages-container {

      .book-page {

        &.left-page {
          background-color: #282828;
          color: var(--light-color);
          &::-webkit-scrollbar {
            width: 10px;
          }

          &::-webkit-scrollbar-track {
            background: #3a3a3a;
          }

          &::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 5px;
          }

          &::-webkit-scrollbar-thumb:hover {
            background: #666;
          }
        }

        &.right-page {
          background-color: #282828;
          color: var(--light-color);
          &::-webkit-scrollbar {
            width: 10px;
          }

          &::-webkit-scrollbar-track {
            background: #3a3a3a;
          }

          &::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 5px;
          }

          &::-webkit-scrollbar-thumb:hover {
            background: #666;
          }
        }
      }
    }
    .book-navigation {
      background-color: #282828;
      .page-indicator {
        color: var(--border);
      }
    }

    .dialog-header {
      .header-actions {
        .book-content-section {
          .contents-btn {
            .library-content-context {
              .conts_item {
                &.active {
                  background-color: var(--book_about) !important;
                  color: white !important;
                }
              }
            }
          }
        }
      }
    }
  }


  &::backdrop {
    background-color: rgba(0, 0, 0, 0.8);
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 60px;
    border-bottom: 1px solid var(--book_about);

    .settings-container{
      .settings-button {
        border: 1px solid var(--text-color);
        border-radius: 15px;
        padding: 8px 22px;
        font-family: Prata;
        font-weight: 400;
        font-size: 30px;
        line-height: 34px;
        letter-spacing: 0;
        text-align: justify;
        transition: background-color 200ms ease-in;
        &:hover {
          background-color: var(--text-color);
        }
      }
      .settings-options {
        position: absolute;
        left: 0;
        border-radius: 15px;
        width: 600px;
        min-height: 360px;
        height: fit-content;
        padding: 40px 20px;
        border: 1px solid var(--text-color);
        font-family: Prata;
        font-weight: 400;
        font-size: 24px;
        line-height: 24px;
        letter-spacing: 0;
        background-color: rgba(255, 251, 242, 1);
        display: flex;
        flex-direction: column;
        gap: 32px;

        .color-themes-options {
          margin: 0 auto;
          .theme-option {
            width: 130px;
            height: 50px;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 24px;
            letter-spacing: 0;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            &.light-option {
              background-color: rgba(255, 251, 242, 1);
              color: var(--font-color1);
            }
            &.dark-option {
              background-color: #282828;
              color: var(--light-color);
            }
            &.sepia-option {
              background-color: rgba(218, 184, 140, 1);
              color: #282828;
            }
            &.active-theme {
              border: 1px solid var(--text-color);
            }
          }
        }
        .fonts-options {
          margin: 0 auto;
          display: flex;
          flex-direction: column;
          gap: 16px;
          align-items: center;
          .font-option {
            color: var(--text-color);
            font-weight: 400;
            font-size: 24px;
            line-height: 24px;
            letter-spacing: 0;
            text-align: center;
            cursor: pointer;
            &.prata-option {
              font-family: Prata;
            }
            &.open-sans-option {
              font-family: 'Open Sans';
            }
            &.montserrat-option {
              font-family: Montserrat;
            }
            &.active-font {
              color: var(--font-color1);
            }
          }
        }
        .text-width-options {
          margin: 0 auto;
          display: flex;
          flex-direction: column;
          gap: 16px;
          align-items: center;
          .width-option {
            color: var(--text-color);
            font-weight: 400;
            font-size: 20px;
            line-height: 24px;
            letter-spacing: 0;
            text-align: center;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            font-family: Prata;

            &:hover {
              background-color: rgba(var(--text-color-rgb), 0.1);
            }

            &.active-width {
              border: 1px solid var(--text-color);
              background-color: rgba(var(--text-color-rgb), 0.1);
            }
          }
        }
      }
    }
    .book-content-section {
      .conts_title {
        font-family: Prata;
        font-weight: 400;
        font-size: 24px;
        line-height: 24px;
        color: var(--font-color1);
        padding: 0 12px 12px 26px;
      }

      .conts_item {
        display: flex;
        align-items: center;
        font-family: Prata;
        font-weight: 400;
        font-size: 18px;
        line-height: 18px;
        color: var(--font-color1);
        height: 44px;
        padding: 0 12px 0 26px;

        &:hover {
          background-color: var(--selection);
        }
      }
    }

    .dialog-title {
      font-family: Prata;
      font-weight: 400;
      font-size: 28px;
      line-height: 34px;
      color: var(--font-color1);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0 12px;
    }

    .close-button {
      background: none;
      border: none;
      cursor: pointer;
      color: var(--font-color1);
      top: 30px;

      &:hover {
        color: var(--book_about);
      }
    }
  }

  .book-pages-container {
    display: flex;
    height: calc(100% - 180px);
    overflow: hidden;

    .book-page {
      flex: 1;
      padding: 40px;
      overflow-y: auto;
      height: 100%;

      &.left-page {
        border-right: 1px solid var(--book_about);
        background-color: rgba(255, 255, 255, 0.95);
        &::-webkit-scrollbar {
          width: 10px;
        }

        &::-webkit-scrollbar-track {
          background: var(--light-color);
        }

        &::-webkit-scrollbar-thumb {
          background: var(--book_about);
          border-radius: 5px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: var(--text-color);
        }

        .text-content-wrapper {
          width: 100%;
          transition: max-width 0.3s ease;
        }
      }

      &.right-page {
        background-color: rgba(255, 255, 255, 0.95);
        &::-webkit-scrollbar {
          width: 10px;
        }

        &::-webkit-scrollbar-track {
          background: var(--light-color);
        }

        &::-webkit-scrollbar-thumb {
          background: var(--book_about);
          border-radius: 5px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: var(--text-color);
        }
      }

      // Book content styling with responsive defaults and proportional line-height
      p, div {
        font-size: var(--fullscreen-font-size, 24px);
        line-height: var(--fullscreen-line-height, 36px);
        margin-bottom: 20px;
        color: var(--text-color);
      }

      h1, h2, h3, h4, h5, h6 {
        color: var(--book_about);
        margin-bottom: 20px;
        line-height: var(--fullscreen-line-height, 36px);
      }
    }
  }

  .book-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    border-top: 1px solid var(--book_about);

    .nav-button {
      display: flex;
      align-items: center;
      gap: 10px;
      background: var(--buttn_catg2);
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      color: white;
      font-family: Prata;
      font-size: 16px;
      cursor: pointer;
      width: 230px;
      background-position: center;
      background-size: contain;
      justify-content: center;

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:hover:not(.disabled) {
        opacity: 0.9;
      }
    }

    .page-indicator {
      font-family: Prata;
      font-size: 16px;
      color: var(--font-color1);
    }
  }

  &.Prata {
    .book-pages-container {
      font-family: Prata !important;

      .fullscreen-text,
      text-interaction,
      .book_text_section {
        font-family: Prata !important;

        ::ng-deep * {
          font-family: Prata !important;
          font-size: inherit !important;
          line-height: inherit !important;
        }

        ::ng-deep p,
        ::ng-deep div,
        ::ng-deep span,
        ::ng-deep h1,
        ::ng-deep h2,
        ::ng-deep h3,
        ::ng-deep h4,
        ::ng-deep h5,
        ::ng-deep h6 {
          font-family: Prata !important;
          font-size: inherit !important;
          line-height: inherit !important;
        }
      }
    }
  }
  &.Open-Sans {
    .book-pages-container {
      font-family: 'Open Sans' !important;

      .fullscreen-text,
      text-interaction,
      .book_text_section {
        font-family: 'Open Sans' !important;

        ::ng-deep * {
          font-family: 'Open Sans' !important;
          font-size: inherit !important;
          line-height: inherit !important;
        }

        ::ng-deep p,
        ::ng-deep div,
        ::ng-deep span,
        ::ng-deep h1,
        ::ng-deep h2,
        ::ng-deep h3,
        ::ng-deep h4,
        ::ng-deep h5,
        ::ng-deep h6 {
          font-family: 'Open Sans' !important;
          font-size: inherit !important;
          line-height: inherit !important;
        }
      }
    }
  }
  &.Montserrat {
    .book-pages-container {
      font-family: Montserrat !important;

      .fullscreen-text,
      text-interaction,
      .book_text_section {
        font-family: Montserrat !important;

        ::ng-deep * {
          font-family: Montserrat !important;
          font-size: inherit !important;
          line-height: inherit !important;
        }

        ::ng-deep p,
        ::ng-deep div,
        ::ng-deep span,
        ::ng-deep h1,
        ::ng-deep h2,
        ::ng-deep h3,
        ::ng-deep h4,
        ::ng-deep h5,
        ::ng-deep h6 {
          font-family: Montserrat !important;
          font-size: inherit !important;
          line-height: inherit !important;
        }
      }
    }
  }
}


.font-size-slider-container {
  width: 100%;
  padding: 10px 0;
  max-width: 450px;
  margin: 0 auto;

  .font-size-label {
    margin-bottom: 10px;
    text-align: center;
    font-size: 14px;
  }
  .scale-box {
    padding-top: 12px;
    .font-size-min {
      font-size: 18px;
    }

    .font-size-medium {
      font-size: 24px;
    }

    .font-size-max {
      font-size: 30px;
    }
  }

  .slider-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;

    .slider-track {
      position: relative;
      width: 100%;
      height: 4px;
      background-color: rgba(217, 217, 217, 1);
      border-radius: 2px;

      .slider-track-active {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        background-color: rgba(222, 165, 61, 1);
        border-radius: 2px;
        pointer-events: none;
      }

      .font-size-slider {
        position: absolute;
        width: 100%;
        height: 4px;
        -webkit-appearance: none;
        appearance: none;
        background: transparent;
        outline: none;
        margin: 0;
        z-index: 2;

        &::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: rgba(222, 165, 61, 1);
          cursor: pointer;
          border: 2px solid rgba(222, 165, 61, 1);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        &::-moz-range-thumb {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: rgba(222, 165, 61, 1);
          cursor: pointer;
          border: 2px solid rgba(222, 165, 61, 1);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
}

// Media queries for responsive design
@media (max-width: 1250px) {
  .fullscreen-book-dialog {
    .dialog-header {
      .settings-container {
        margin: 0;
      }
      .header-actions {
        gap: 32px;
        .book-content-section {
          .contents-btn {
            .btn-label {
              display: none !important;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .library-content-context.contents_ {
    right: -20px;
  }

  .related_books_section {
    overflow: hidden;
  }

  .library-content-context.contents_::before {
    left: 92.5%;
  }

  .fullscreen-book-dialog {
    // Responsive default font size for tablet
    .book-pages-container .book-page {
      --fullscreen-font-size: 18px;
      --fullscreen-line-height: 27px;

      // Book content styling with smaller defaults for tablet
      p, div {
        font-size: var(--fullscreen-font-size, 18px);
        line-height: var(--fullscreen-line-height, 27px);
      }

      h1, h2, h3, h4, h5, h6 {
        line-height: var(--fullscreen-line-height, 27px);
      }
    }
    &-active {
      display: flex;
      flex-direction: column;
    }
    .dialog-header {
      zoom: 0.8;
      position: relative;
      z-index: 10000;

      .dialog-title {
        font-size: 24px;
      }
      .settings-container {
        position: relative;
        z-index: 10001;

        .settings-options {
          width: 530px;
          z-index: 10002 !important;
          position: absolute !important;
          background-color: rgba(255, 251, 242, 1) !important;
          border: 1px solid var(--text-color) !important;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;

          .color-themes-options,
          .fonts-options,
          .font-size-slider-container,
          .text-width-options {
            position: relative !important;
            z-index: 10003 !important;
          }

          .theme-option,
          .font-option,
          .width-option {
            position: relative !important;
            z-index: 10004 !important;
            cursor: pointer !important;
            pointer-events: auto !important;
            background-color: transparent !important;
          }

          input[type="range"] {
            position: relative !important;
            z-index: 10004 !important;
            pointer-events: auto !important;
          }
        }
      }
      .header-actions {
        gap: 22px;
        .book-content-section {
          .contents-btn {
            display: flex !important;

            .library-content-context {
              .conts_item {
                &.active {
                  background-color: var(--book_about) !important;
                  color: white !important;
                }
              }
            }
          }
        }
      }
    }
  }


    .book-pages-container {
      flex-direction: column;
      flex: 1 0 0;
      overflow-y: auto;
      padding: 20px 40px;

      .text-content-wrapper {
        width: 100%;
        transition: max-width 0.3s ease;
      }


      // Глобальные стили для всего текста в fullscreen режиме
      ::ng-deep * {
        font-family: var(--fullscreen-font-family) !important;
      }

      // Принудительно применяем размер шрифта ко всем элементам с максимальной специфичностью
      ::ng-deep text-interaction * {
        font-size: inherit !important;
      }

      ::ng-deep text-interaction p,
      ::ng-deep text-interaction div,
      ::ng-deep text-interaction span,
      ::ng-deep text-interaction h1,
      ::ng-deep text-interaction h2,
      ::ng-deep text-interaction h3,
      ::ng-deep text-interaction h4,
      ::ng-deep text-interaction h5,
      ::ng-deep text-interaction h6 {
        font-size: inherit !important;
      }

      // Сохраняем жирность для жирных элементов
      ::ng-deep text-interaction strong,
      ::ng-deep text-interaction b,
      ::ng-deep text-interaction .bold {
        font-weight: bold !important;
      }

      .book-page {
        &.left-page {
          border: none;
          width: 100%;
          max-width: none;

          .fullscreen-text {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;


            // Принудительно применяем размер шрифта, line-height и семейство шрифтов ко всему тексту
            ::ng-deep * {
              font-family: var(--fullscreen-font-family, inherit) !important;
              font-size: inherit !important;
              line-height: inherit !important;
            }

            ::ng-deep p,
            ::ng-deep div,
            ::ng-deep span,
            ::ng-deep h1,
            ::ng-deep h2,
            ::ng-deep h3,
            ::ng-deep h4,
            ::ng-deep h5,
            ::ng-deep h6,
            ::ng-deep .text-content,
            ::ng-deep .chapter-content {
              font-family: var(--fullscreen-font-family, inherit) !important;
              font-size: inherit !important;
              line-height: inherit !important;
            }

            // Сохраняем жирность для жирных элементов
            ::ng-deep strong,
            ::ng-deep b,
            ::ng-deep .bold {
              font-weight: bold !important;
            }
          }
        }
        &.right-page {
          display: none;
        }
      }
    }

    .book-navigation {
      // flex-direction: column;
      // gap: 15px;
      padding: 15px 30px;
      .page-indicator {
        font-size: 12px;
      }

      .nav-button {
        width: 100%;
        justify-content: center;
        background: url(assets/images/icons/arrow-in-circle.svg);
        width: 34px;
        height: 34px;
        padding: 0;
        &.next-button {
          transform: rotate(180deg);
        }
        .btn-label {
          display: none;
        }
      }
    }
  }

@media (max-width: 600px) {
  .library-content-context.contents_::before {
    left: 93.5%;
  }

  .library-content-context.contents_ {
    right: -15px;
    width: 430px;
  }
}

// Show fullscreen button on desktop
@media (min-width: 501px) {
  .hidden-mobile {
    visibility: visible !important;
    pointer-events: auto !important;
  }
}

@media (max-width: 500px) {
  .library-page{
    .middle_stripe {
      .wrapper_line.custom_l:has(.library) {
        padding: 0 0 86px 0 !important;
      }
    }
  }
  .tabs_w {
    margin: -45px -10px 0;
  }

  .library-content-context.contents_ {
    width: 350px;
  }

  .library-content-context.contents_::before {
      left: 91.5%;
      top: 1px;
      width: 38px;
  }
}

@media (max-width: 480px) {
  .library-tabs {
    max-width: 100%;
  }

  .fullscreen-book-dialog {
    // Responsive default font size for mobile (same as tablet)
    .book-pages-container .book-page {
      --fullscreen-font-size: 18px;
      --fullscreen-line-height: 27px;

      // Book content styling with smaller defaults for mobile
      p, div {
        font-size: var(--fullscreen-font-size, 18px);
        line-height: var(--fullscreen-line-height, 27px);
      }

      h1, h2, h3, h4, h5, h6 {
        line-height: var(--fullscreen-line-height, 27px);
      }
    }
    .dialog-header {
      padding: 15px 60px 15px 40px;
      .settings-container {
        .settings-button {
          padding: 6px 20px;
          font-size: 24px;
          line-height: 32px;

        }
        .settings-options {
          width: 410px;
          height: 340px;
          padding: 25px 15px;
        }
      }
      
      .close-button {
        top: 23px;
      }

    }
    .book-navigation {
      padding: 12px 24px;
    }
  }

}

@media (max-width: 420px) {
  .tabs_w {
    margin: -220px -15px 0;
  }

  .book-text-header .conts_title {
    font-size: 21px;
    line-height: 23px;
  }

  .book-text-header .conts_item {
    font-size: 16px;
  }
}

@media (max-width: 370px) {
  .library-content-context.contents_ {
    width: 300px;
  }

  .library-content-context.contents_::before {
    top: 3px;
    width: 43px;
  }
}
