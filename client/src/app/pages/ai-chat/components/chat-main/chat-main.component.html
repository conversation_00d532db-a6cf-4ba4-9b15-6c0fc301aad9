<div class="chat-main">
  @if (chat()) {
    <!-- Cha<PERSON> Header -->
    <div class="chat-header">
      <h2 class="chat-title">{{ chatTitle() }}</h2>
    </div>

    <!-- Messages Container -->
    <div class="messages-container" #messagesContainer>
      @if (hasMessages()) {
        <!-- Message List -->
        <div class="messages-list">
          @for (message of chat()!.messages; track message.id) {
            <app-chat-message
              [message]="message"
              [isLoading]="!!(message.isTyping && isLoading())"
              (sourceClick)="onSourceClick($event)"
            ></app-chat-message>
          }
        </div>
      } @else {
        <!-- Welcome State -->
        <div class="welcome-state">
          <div class="welcome-content">
            <div class="welcome-icon">🧘‍♂️</div>
            <h3 class="welcome-title">Добро пожаловать в AI-чат</h3>
            <p class="welcome-description">
              Задайте любой вопрос о духовных практиках, философии или традициях. 
              Я помогу найти ответы в нашей библиотеке знаний.
            </p>
            <div class="welcome-suggestions">
              <button 
                class="suggestion-btn"
                (click)="messageText.set('Расскажи о медитации для начинающих')"
              >
                Медитация для начинающих
              </button>
              <button 
                class="suggestion-btn"
                (click)="messageText.set('Что такое адвайта-веданта?')"
              >
                Что такое адвайта-веданта?
              </button>
              <button 
                class="suggestion-btn"
                (click)="messageText.set('Как начать духовную практику?')"
              >
                Как начать духовную практику?
              </button>
            </div>
          </div>
        </div>
      }
    </div>

    <!-- Input Area -->
    <div class="input-area">
      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            #messageInput
            class="message-input"
            [(ngModel)]="messageText"
            (keydown)="onKeyDown($event)"
            (input)="onInput($event)"
            placeholder="Введите ваше сообщение..."
            rows="1"
            [disabled]="isLoading()"
          ></textarea>
          
          <button
            class="send-button"
            (click)="onSendMessage()"
            [disabled]="!messageText().trim() || isLoading()"
            [title]="isLoading() ? 'Ожидание ответа...' : 'Отправить сообщение'"
          >
            @if (isLoading()) {
              <div class="loading-spinner"></div>
            } @else {
              <span class="send-icon">➤</span>
            }
          </button>
        </div>
        
        @if (isLoading()) {
          <div class="loading-indicator">
            <span class="loading-text">{{ getLoadingText() }}</span>
            <div class="loading-dots">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
          </div>
        }
      </div>
    </div>
  } @else {
    <!-- No Chat Selected -->
    <div class="no-chat-state">
      <div class="no-chat-content">
        <div class="no-chat-icon">💬</div>
        <h3 class="no-chat-title">Выберите чат или создайте новый</h3>
        <p class="no-chat-description">
          Начните новый диалог с AI-ассистентом или продолжите существующий разговор
        </p>
      </div>
    </div>
  }
</div>
