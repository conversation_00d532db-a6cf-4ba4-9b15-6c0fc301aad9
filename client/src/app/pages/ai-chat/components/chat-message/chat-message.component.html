<div class="message-wrapper" [class.user-message]="message().role === 'user'" [class.assistant-message]="message().role === 'assistant'">
  
  <!-- User Message -->
  @if (message().role === 'user') {
    <div class="message user-msg">
      <div class="message-content">
        <div class="message-text" [innerHTML]="formatMarkdown(message().content)"></div>
        <div class="message-time">{{ formatTime(message().timestamp) }}</div>
      </div>
      <div class="message-avatar user-avatar">
        <span class="avatar-icon">👤</span>
      </div>
    </div>
  }

  <!-- Assistant Message -->
  @if (message().role === 'assistant') {
    <div class="message assistant-msg">
      <div class="message-avatar assistant-avatar">
        <span class="avatar-icon">🧘‍♂️</span>
      </div>
      <div class="message-content">
        @if (message().isTyping && isLoading()) {
          <!-- Typing Indicator -->
          <div class="typing-indicator">
            <div class="typing-dots">
              <span class="typing-dot"></span>
              <span class="typing-dot"></span>
              <span class="typing-dot"></span>
            </div>
            <span class="typing-text">AI печатает...</span>
          </div>
        } @else {
          <!-- Message Content -->
          <div class="message-text">
            @for (part of parseContent(message().content); track $index) {
              @if (part.type === 'text') {
                <span [innerHTML]="formatMarkdown(part.content)"></span>
              } @else if (part.type === 'citation' && part.source) {
                <button 
                  class="citation-link"
                  (click)="onSourceClick(part.source!)"
                  [title]="part.source!.title"
                >
                  {{ part.content }}
                </button>
              }
            }
          </div>

          <!-- Sources Section -->
          @if (message().sources && message().sources!.length > 0) {
            <div class="sources-section">
              <div class="sources-title">Источники:</div>
              <div class="sources-list">
                @for (source of message().sources!; track source.id; let i = $index) {
                  <button 
                    class="source-item"
                    (click)="onSourceClick(source)"
                  >
                    <span class="source-number">{{ i + 1 }}</span>
                    <span class="source-title">{{ source.title }}</span>
                  </button>
                }
              </div>
            </div>
          }

          <!-- Message Actions -->
          <div class="message-actions">
            <button 
              class="action-btn copy-btn"
              (click)="onCopyMessage()"
              [class.copied]="isCopied()"
              [title]="isCopied() ? 'Скопировано!' : 'Копировать'"
            >
              @if (isCopied()) {
                <span class="action-icon">✓</span>
              } @else {
                <span class="action-icon copy-icon">📋</span>
              }
            </button>
            <div class="message-time">{{ formatTime(message().timestamp) }}</div>
          </div>
        }
      </div>
    </div>
  }
</div>
