@use "../../../../styles/core.scss" as core;

.message-wrapper {
  width: 100%;
  margin-bottom: 20px;

  &.user-message {
    display: flex;
    justify-content: flex-end;
  }

  &.assistant-message {
    display: flex;
    justify-content: flex-start;
  }
}

.message {
  display: flex;
  max-width: 85%;
  gap: 16px;
  align-items: flex-start;
}

// Message Avatars
.message-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 24px;
  border: 3px solid var(--border);
  box-shadow: 0 4px 12px rgba(83, 46, 0, 0.15);
}

.user-avatar {
  background: linear-gradient(135deg, core.$light2 0%, core.$light4 100%);
  color: white;
  order: 2;
}

.assistant-avatar {
  background: linear-gradient(135deg, var(--selection) 0%, var(--light-color) 100%);
  color: var(--font-color1);
  order: 1;
}

// Message Content
.message-content {
  flex: 1;
  min-width: 0;
}

.user-msg .message-content {
  order: 1;
}

.assistant-msg .message-content {
  order: 2;
}

// Message Text
.message-text {
  padding: 20px 24px;
  border-radius: 18px;
  font-family: IBM_Plex_Sans, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
  box-shadow: 0 4px 16px rgba(83, 46, 0, 0.1);
  border: 2px solid transparent;

  // Markdown styles
  strong {
    font-weight: 600;
    color: var(--font-color1);
  }

  em {
    font-style: italic;
    color: var(--text-color);
  }

  code {
    background: rgba(222, 165, 61, 0.2);
    padding: 4px 8px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    border: 1px solid var(--border);
  }

  pre {
    background: rgba(249, 233, 200, 0.5);
    padding: 16px 20px;
    border-radius: 12px;
    overflow-x: auto;
    margin: 12px 0;
    border: 2px solid var(--border);

    code {
      background: none;
      padding: 0;
      border: none;
    }
  }
}

.user-msg .message-text {
  background: linear-gradient(135deg, core.$light2 0%, core.$light4 100%);
  color: white;
  border-bottom-right-radius: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.assistant-msg .message-text {
  background: rgba(255, 255, 255, 0.9);
  color: var(--font-color1);
  border: 2px solid var(--border);
  border-bottom-left-radius: 6px;
}

// Citation Links
.citation-link {
  display: inline;
  background: none;
  border: none;
  color: rgba(42, 124, 187, 1);
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  padding: 0;
  margin: 0 2px;
  transition: color 0.2s ease;

  &:hover {
    color: rgba(105, 77, 164, 1);
  }

  &:visited {
    color: rgba(105, 77, 164, 1);
  }
}

// Sources Section
.sources-section {
  margin-top: 12px;
  padding: 12px 16px;
  background: var(--selection);
  border-radius: 8px;
  border: 1px solid var(--border);
}

.sources-title {
  font-family: Prata;
  font-size: 14px;
  font-weight: 400;
  color: var(--font-color1);
  margin-bottom: 8px;
}

.sources-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.source-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  background: transparent;
  border: 1px solid var(--border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;

  &:hover {
    background: var(--dr-back);
    border-color: core.$light2;
  }
}

.source-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: core.$light2;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.source-title {
  font-family: IBM_Plex_Sans;
  font-size: 13px;
  color: var(--font-color1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Message Actions
.message-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  padding: 0 4px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.6;

  &:hover {
    opacity: 1;
    background: var(--selection);
  }

  &.copied {
    opacity: 1;
    background: rgba(58, 194, 121, 0.1);
    
    .action-icon {
      color: rgba(58, 194, 121, 1);
    }
  }
}

.action-icon {
  font-size: 14px;
  color: var(--font-color1);
}

.copy-icon {
  font-size: 12px;
}

.message-time {
  font-family: IBM_Plex_Sans;
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.6;
}

// Typing Indicator
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--dr-back);
  border: 1px solid var(--border);
  border-radius: 16px;
  border-bottom-left-radius: 4px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  background: var(--font-color1);
  border-radius: 50%;
  animation: typing-pulse 1.4s ease-in-out infinite both;

  &:nth-child(1) { animation-delay: -0.32s; }
  &:nth-child(2) { animation-delay: -0.16s; }
  &:nth-child(3) { animation-delay: 0s; }
}

@keyframes typing-pulse {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

.typing-text {
  font-family: IBM_Plex_Sans;
  font-size: 14px;
  color: var(--font-color1);
  opacity: 0.7;
}

// Mobile Styles
@media (max-width: 768px) {
  .message {
    max-width: 90%;
    gap: 8px;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  .message-text {
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 12px;
  }

  .user-msg .message-text {
    border-bottom-right-radius: 4px;
  }

  .assistant-msg .message-text {
    border-bottom-left-radius: 4px;
  }

  .sources-section {
    padding: 10px 12px;
  }

  .sources-title {
    font-size: 13px;
  }

  .source-item {
    padding: 5px 6px;
  }

  .source-number {
    width: 18px;
    height: 18px;
    font-size: 11px;
  }

  .source-title {
    font-size: 12px;
  }

  .typing-indicator {
    padding: 12px 16px;
    border-radius: 12px;
    border-bottom-left-radius: 4px;
  }

  .typing-text {
    font-size: 13px;
  }
}
