<div class="chat-sidebar">
  <!-- Header with New Chat Button -->
  <div class="sidebar-header">
    <button class="new-chat-btn" (click)="onNewChatClick()">
      <span class="new-chat-icon">+</span>
      <span class="new-chat-text">Новый чат</span>
    </button>
  </div>

  <!-- Chat List -->
  <div class="chat-list-container">
    @if (hasChats()) {
      <div class="chat-list">
        @for (chat of sortedChats(); track chat.id) {
          <div 
            class="chat-item"
            [class.active]="currentChatId() === chat.id"
            [class.pinned]="chat.isPinned"
            (click)="onChatClick(chat.id)"
          >
            <!-- Pin indicator -->
            @if (chat.isPinned) {
              <div class="pin-indicator">
                <span class="pin-icon">📌</span>
              </div>
            }

            <!-- Chat content -->
            <div class="chat-content">
              @if (editingChatId() === chat.id) {
                <!-- Editing mode -->
                <input
                  type="text"
                  class="chat-title-input"
                  [(ngModel)]="editingTitle"
                  (keydown)="onKeydown($event, chat.id)"
                  (blur)="onSaveRename(chat.id)"
                  (click)="$event.stopPropagation()"
                  #titleInput
                />
              } @else {
                <!-- Display mode -->
                <div class="chat-info">
                  <div class="chat-title">{{ getChatTitle(chat) }}</div>
                  <div class="chat-date">{{ formatDate(chat.updatedAt) }}</div>
                </div>
              }
            </div>

            <!-- Action buttons (shown on hover) -->
            <div class="chat-actions">
              @if (!chat.isPinned) {
                <button 
                  class="action-btn pin-btn"
                  (click)="onPinChat(chat.id, $event)"
                  title="Закрепить"
                >
                  <span class="action-icon pin-icon">📌</span>
                </button>
              } @else {
                <button 
                  class="action-btn unpin-btn"
                  (click)="onPinChat(chat.id, $event)"
                  title="Открепить"
                >
                  <span class="action-icon unpin-icon">📌</span>
                </button>
              }

              <button 
                class="action-btn rename-btn"
                (click)="onStartRename(chat, $event)"
                title="Переименовать"
              >
                <span class="action-icon rename-icon">✏️</span>
              </button>

              @if (!chat.isPinned) {
                <button 
                  class="action-btn delete-btn"
                  (click)="onDeleteChat(chat.id, $event)"
                  title="Удалить"
                >
                  <span class="action-icon delete-icon">🗑️</span>
                </button>
              }
            </div>
          </div>
        }
      </div>
    } @else {
      <!-- Empty state -->
      <div class="empty-state">
        <div class="empty-icon">💬</div>
        <div class="empty-title">Нет чатов</div>
        <div class="empty-description">
          Создайте новый чат, чтобы начать общение с AI-ассистентом
        </div>
      </div>
    }
  </div>
</div>
