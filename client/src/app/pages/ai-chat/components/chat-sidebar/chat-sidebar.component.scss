@use "../../../../styles/core.scss" as core;

.chat-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--side_back);
  color: var(--font-color1);
}

// Header
.sidebar-header {
  padding: 24px 20px;
  border-bottom: 2px solid var(--border);
  background: linear-gradient(135deg, var(--light-color) 0%, var(--side_back) 100%);
}

.new-chat-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 14px 20px;
  background: var(--button_);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid core.$light2;
  border-radius: 12px;
  color: var(--font-color1);
  font-family: Prata, serif;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(83, 46, 0, 0.15);

  &:hover {
    background: var(--button_figure);
    background-size: cover;
    color: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(83, 46, 0, 0.25);
  }

  .new-chat-icon {
    font-size: 20px;
    font-weight: bold;
  }
}

// Chat List Container
.chat-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 12px 0;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(249, 233, 200, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--text-color);
    border-radius: 4px;
    border: 2px solid var(--side_back);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: core.$light2;
  }
}

// Chat List
.chat-list {
  padding: 0 16px;
}

// Chat Item
.chat-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 16px 18px;
  margin-bottom: 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.4);

  &:hover {
    background: var(--selection);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(83, 46, 0, 0.15);

    .chat-actions {
      opacity: 1;
      visibility: visible;
    }
  }

  &.active {
    background: var(--selection);
    border-color: core.$light2;
    box-shadow: 0 4px 16px rgba(222, 165, 61, 0.3);
  }

  &.pinned {
    border-left: 4px solid core.$light2;
    background: linear-gradient(135deg, var(--selection) 0%, rgba(255, 226, 163, 0.8) 100%);
  }
}

// Pin Indicator
.pin-indicator {
  margin-right: 8px;
  
  .pin-icon {
    font-size: 12px;
    opacity: 0.7;
  }
}

// Chat Content
.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-title {
  font-family: Prata;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.3;
  color: var(--font-color1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-date {
  font-family: IBM_Plex_Sans;
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.7;
}

.chat-title-input {
  width: 100%;
  background: var(--grey-back);
  border: 1px solid core.$light2;
  border-radius: 4px;
  padding: 4px 8px;
  font-family: Prata;
  font-size: 14px;
  color: var(--font-color1);
  outline: none;

  &:focus {
    border-color: core.$light4;
  }
}

// Chat Actions
.chat-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--grey-back);
  }

  .action-icon {
    font-size: 12px;
    opacity: 0.7;
  }
}

.pin-btn .pin-icon {
  opacity: 0.5;
}

.unpin-btn .unpin-icon {
  opacity: 1;
  color: core.$light2;
}

.delete-btn:hover {
  background: rgba(194, 30, 17, 0.1);
  
  .delete-icon {
    opacity: 1;
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  height: 100%;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-family: Prata;
  font-size: 18px;
  font-weight: 400;
  color: var(--font-color1);
  margin-bottom: 8px;
}

.empty-description {
  font-family: IBM_Plex_Sans;
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.7;
  line-height: 1.4;
  max-width: 200px;
}

// Mobile Styles
@media (max-width: 768px) {
  .sidebar-header {
    padding: 16px 12px;
  }

  .new-chat-btn {
    padding: 10px 12px;
    font-size: 14px;
  }

  .chat-item {
    padding: 10px 12px;
  }

  .chat-title {
    font-size: 13px;
  }

  .chat-date {
    font-size: 11px;
  }

  .empty-state {
    padding: 30px 16px;
  }

  .empty-icon {
    font-size: 40px;
  }

  .empty-title {
    font-size: 16px;
  }

  .empty-description {
    font-size: 13px;
  }
}
