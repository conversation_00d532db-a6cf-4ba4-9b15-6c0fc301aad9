import { Component, input, output, signal, computed, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Chat } from '../../ai-chat.component';

@Component({
  selector: 'app-chat-sidebar',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './chat-sidebar.component.html',
  styleUrl: './chat-sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChatSidebarComponent {
  // Inputs
  chats = input<Chat[]>([]);
  currentChatId = input<string | null>(null);

  // Outputs
  newChat = output<void>();
  chatSelect = output<string>();
  chatRename = output<{ chatId: string; newTitle: string }>();
  chatDelete = output<string>();
  chatPin = output<string>();

  // State
  editingChatId = signal<string | null>(null);
  editingTitle = signal<string>('');

  // Computed
  sortedChats = computed(() => {
    const allChats = this.chats();
    const pinned = allChats.filter(chat => chat.isPinned);
    const unpinned = allChats.filter(chat => !chat.isPinned);
    
    // Sort pinned chats by updatedAt (most recent first)
    pinned.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
    
    // Sort unpinned chats by updatedAt (most recent first)
    unpinned.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
    
    return [...pinned, ...unpinned];
  });

  hasChats = computed(() => this.chats().length > 0);

  onNewChatClick() {
    console.log('New chat button clicked in sidebar');
    this.newChat.emit();
  }

  onChatClick(chatId: string) {
    console.log('Chat clicked in sidebar:', chatId, 'editing:', this.editingChatId());
    if (this.editingChatId() !== chatId) {
      console.log('Emitting chat select:', chatId);
      this.chatSelect.emit(chatId);
    }
  }

  onStartRename(chat: Chat, event: Event) {
    event.stopPropagation();
    this.editingChatId.set(chat.id);
    this.editingTitle.set(chat.title);
  }

  onSaveRename(chatId: string) {
    const newTitle = this.editingTitle().trim();
    if (newTitle && newTitle !== this.chats().find(c => c.id === chatId)?.title) {
      this.chatRename.emit({ chatId, newTitle });
    }
    this.editingChatId.set(null);
    this.editingTitle.set('');
  }

  onCancelRename() {
    this.editingChatId.set(null);
    this.editingTitle.set('');
  }

  onDeleteChat(chatId: string, event: Event) {
    event.stopPropagation();
    if (confirm('Вы уверены, что хотите удалить этот чат?')) {
      this.chatDelete.emit(chatId);
    }
  }

  onPinChat(chatId: string, event: Event) {
    event.stopPropagation();
    this.chatPin.emit(chatId);
  }

  onKeydown(event: KeyboardEvent, chatId: string) {
    if (event.key === 'Enter') {
      this.onSaveRename(chatId);
    } else if (event.key === 'Escape') {
      this.onCancelRename();
    }
  }

  formatDate(date: Date): string {
    const now = new Date();
    const diffInHours = (now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return new Date(date).toLocaleTimeString('ru-RU', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffInHours < 24 * 7) {
      return new Date(date).toLocaleDateString('ru-RU', { 
        weekday: 'short' 
      });
    } else {
      return new Date(date).toLocaleDateString('ru-RU', { 
        day: '2-digit', 
        month: '2-digit' 
      });
    }
  }

  getChatTitle(chat: Chat): string {
    if (chat.title && chat.title.trim()) {
      return chat.title;
    }
    
    // Generate title from first user message
    const firstUserMessage = chat.messages.find(m => m.role === 'user');
    if (firstUserMessage) {
      const title = firstUserMessage.content.slice(0, 30);
      return title.length < firstUserMessage.content.length ? title + '...' : title;
    }
    
    return 'Новый чат';
  }
}
