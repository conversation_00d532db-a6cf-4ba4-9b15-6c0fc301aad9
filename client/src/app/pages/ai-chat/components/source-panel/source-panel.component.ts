import { Component, input, output, signal, computed, OnInit, OnChanges, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChatSource } from '../../ai-chat.component';

@Component({
  selector: 'app-source-panel',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './source-panel.component.html',
  styleUrl: './source-panel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SourcePanelComponent implements OnInit, OnChanges {
  // Inputs
  source = input<ChatSource | null>(null);
  isOpen = input<boolean>(false);

  // Outputs
  close = output<void>();

  // State
  highlightedContent = signal<string>('');

  // Computed
  hasSource = computed(() => this.source() !== null);

  ngOnInit() {
    // Watch for source changes to update highlighted content
    this.updateHighlightedContent();
  }

  ngOnChanges() {
    this.updateHighlightedContent();
  }

  onClose() {
    this.close.emit();
  }

  onBackdropClick(event: Event) {
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  private updateHighlightedContent() {
    const currentSource = this.source();
    if (!currentSource) {
      this.highlightedContent.set('');
      return;
    }

    let content = currentSource.content;
    
    // Highlight the quoted text if available
    if (currentSource.highlightedText) {
      const highlightedText = currentSource.highlightedText;
      const regex = new RegExp(`(${this.escapeRegExp(highlightedText)})`, 'gi');
      content = content.replace(regex, '<mark class="highlight">$1</mark>');
    }

    this.highlightedContent.set(content);
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  scrollToHighlight() {
    setTimeout(() => {
      const highlightElement = document.querySelector('.source-content .highlight');
      if (highlightElement) {
        highlightElement.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
      }
    }, 100);
  }

  formatContent(content: string): string {
    return content
      // Convert line breaks to HTML
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      // Wrap in paragraphs
      .replace(/^/, '<p>')
      .replace(/$/, '</p>')
      // Clean up empty paragraphs
      .replace(/<p><\/p>/g, '')
      .replace(/<p><br><\/p>/g, '');
  }
}
