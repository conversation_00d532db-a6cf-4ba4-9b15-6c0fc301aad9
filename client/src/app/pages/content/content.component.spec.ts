import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ContentComponent } from './content.component';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslocoService } from '@jsverse/transloco';
import { ContentService } from '@/services/content.service';
import { ProfileService } from '@/services/profile.service';
import { AuthService } from '@/services/auth.service';
import { ToasterService } from '@/services/toaster.service';
import { ShareDataService } from '@/services/share-data.service';
import { SeoService } from '@/services/seo.service';
import { PLATFORM_ID } from '@angular/core';
import { of } from 'rxjs';

describe('ContentComponent', () => {
  let component: ContentComponent;
  let fixture: ComponentFixture<ContentComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockContentService: jasmine.SpyObj<ContentService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate', 'navigateByUrl']);
    const contentServiceSpy = jasmine.createSpyObj('ContentService', ['getContentPreview']);
    const activatedRouteSpy = jasmine.createSpyObj('ActivatedRoute', [], {
      snapshot: { params: { page: 'test-page' } }
    });
    const translocoServiceSpy = jasmine.createSpyObj('TranslocoService', ['getActiveLang']);
    const profileServiceSpy = jasmine.createSpyObj('ProfileService', ['getProfile']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['isAuthenticated']);
    const toasterServiceSpy = jasmine.createSpyObj('ToasterService', ['showToast']);
    const shareDataServiceSpy = jasmine.createSpyObj('ShareDataService', ['setData']);
    const seoServiceSpy = jasmine.createSpyObj('SeoService', ['updateSeo']);

    await TestBed.configureTestingModule({
      imports: [ContentComponent],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: ContentService, useValue: contentServiceSpy },
        { provide: ActivatedRoute, useValue: activatedRouteSpy },
        { provide: TranslocoService, useValue: translocoServiceSpy },
        { provide: ProfileService, useValue: profileServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: ToasterService, useValue: toasterServiceSpy },
        { provide: ShareDataService, useValue: shareDataServiceSpy },
        { provide: SeoService, useValue: seoServiceSpy },
        { provide: PLATFORM_ID, useValue: 'browser' }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ContentComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockContentService = TestBed.inject(ContentService) as jasmine.SpyObj<ContentService>;
    
    // Setup default return values
    translocoServiceSpy.getActiveLang.and.returnValue('en');
    contentServiceSpy.getContentPreview.and.returnValue(of({
      content: '<p>Test content</p>',
      preview: { name: 'test.jpg' },
      category: { id: 1 },
      slug: 'test-slug'
    }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should hide preview when hidePreview is called', () => {
    // Arrange
    component.previewData = 'some preview data';
    component.hrefPreview = 'http://example.com';
    component['previewTimeout'] = setTimeout(() => {}, 1000);

    // Act
    component.hidePreview();

    // Assert
    expect(component.previewData).toBeNull();
    expect(component.hrefPreview).toBe('');
  });

  it('should handle mobile click correctly when preview is already shown', () => {
    // Arrange
    spyOnProperty(window, 'innerWidth', 'get').and.returnValue(500); // Mobile width
    component.previewData = 'existing preview';
    component.hrefPreview = 'http://example.com';
    
    const mockEvent = {
      target: {
        tagName: 'A',
        getAttribute: jasmine.createSpy('getAttribute').and.returnValue('http://example.com'),
        closest: jasmine.createSpy('closest').and.returnValue({
          getAttribute: jasmine.createSpy('getAttribute').and.returnValue('/test-route')
        })
      },
      preventDefault: jasmine.createSpy('preventDefault'),
      stopPropagation: jasmine.createSpy('stopPropagation')
    } as any;

    mockEvent.target.getAttribute.and.callFake((attr: string) => {
      if (attr === 'href') return 'http://example.com';
      if (attr === 'routerLink') return '/test-route';
      return null;
    });

    // Act
    component.handleLinkClick(mockEvent);

    // Assert
    expect(mockRouter.navigateByUrl).toHaveBeenCalledWith('/test-route');
    expect(component.previewData).toBeNull();
    expect(component.hrefPreview).toBe('');
  });

  it('should show preview on mobile when no preview is currently shown', () => {
    // Arrange
    spyOnProperty(window, 'innerWidth', 'get').and.returnValue(500); // Mobile width
    component.previewData = null;
    spyOn(component, 'showPagePreview');
    
    const mockEvent = {
      target: {
        tagName: 'A',
        getAttribute: jasmine.createSpy('getAttribute').and.returnValue('http://example.com'),
        closest: jasmine.createSpy('closest').and.returnValue(null)
      },
      preventDefault: jasmine.createSpy('preventDefault'),
      stopPropagation: jasmine.createSpy('stopPropagation')
    } as any;

    // Act
    component.handleLinkClick(mockEvent);

    // Assert
    expect(component.showPagePreview).toHaveBeenCalledWith(mockEvent);
  });
});
