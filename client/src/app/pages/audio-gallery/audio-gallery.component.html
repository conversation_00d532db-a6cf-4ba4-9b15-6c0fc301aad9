<dialog class="stylized_wide h_auto" #modal>
  <div (click)="closeModal(modal)" class="x_bt"></div>
  <div class="flex flex-col cont_mod">
    <p class="pr_20 text-center">Фильтры</p>
    <div class="format-options">
      <div class="checkbox-container" *ngFor="let option of formatOptions">
        <input class="checkbox_" type="checkbox" [id]="'format-' + option.id" [value]="option.value"
          [checked]="filters.format.includes(option.value)" (change)="changeFormatFilter($event)">
        <span [id]="'format-' + option.id" (click)="changeFormatFilter($event)"></span>
        <label [for]="'format-' + option.id">{{ option.name }}</label>
      </div>
    </div>

    <p class="auth_head a_mg_modal">
      Выбор тегов
    </p>
    <app-custom-dropdown [type]="'multiselect'" [options]="audioService.tags" [selected]="selectedTags"
      (selectedChange)="changeTagFilter($event)" class="a">
    </app-custom-dropdown>

    <p class="auth_head a_mg_modal">
      Автор
    </p>
    <app-custom-dropdown [type]="'multiselect'" [options]="authorsArray" [selected]="selectedAuthors"
      (selectedChange)="changeAuthorFilter($event)" [placeholderText]="'Выбор автора'" class="a">
    </app-custom-dropdown>

    <p class="auth_head a_mg_modal">
      Год
    </p>
    <app-custom-dropdown [type]="'multiselect'" [options]="dataOptions" [selected]="selectedYears"
      (selectedChange)="setCalendar($event)" [placeholderText]="'Выбор года'" class="a">
    </app-custom-dropdown>

    <div class="filter-buttons-container flex justify-between mt-4">
      <button class="save-btn" (click)="resetAndCloseModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Сбросить все</div>
      </button>
      <button class="save-btn" (click)="closeModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Показать</div>
      </button>
    </div>
  </div>
</dialog>
<div *ngIf="audioService.data()">
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Лекции</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <div class="articles-search relative">
          <input [(ngModel)]='filters.description' type="text" placeholder="Поиск" (input)="searchSubject.next($event)">
          <div (click)="openModal()" class="p_filter">
            <span>Фильтр</span>
          </div>
          <div class="articles-sort_ custom-dropdown" (click)="toggleDropdownSort()">
            @if (dropdownSortOpen) {
            <div class="dropdown-content">
              @for(option of sortOptions; track option.id) {
              <div (click)="selectSort(option.value)" class="dropdown-item cat_i"
                [class.active]="currentSortField === option.value">
                {{ option.label }}
                <span class="sort-arrow" *ngIf="currentSortField === option.value">
                  <svg [ngClass]="{'rotate-down': sortDirection === 'Desc', 'rotate-up': sortDirection === 'Asc'}"
                    width="30" height="30" viewBox="0 0 24 24">
                    <path d="M7 10l5 5 5-5H7z" />
                  </svg>
                </span>
              </div>
              }
            </div>
            }
          </div>
        </div>

        <!-- <div class="flex flex-col"> <label for="start-date" style="color: white">Длительность от, мин.:</label>
            <input type="number" [(ngModel)]="filters.durationFrom" (input)="audioService.applyFilters(filters)" />
          </div>

          <div class="flex flex-col"> <label for="start-date" style="color: white">Длительность до, мин.:</label>
            <input type="number" [(ngModel)]="filters.durationTo" (input)="audioService.applyFilters(filters)" />
          </div> -->

        <ng-container>
          <ng-container>
            <div class="itm_a_wrap">
              @for(track of data; track track.id; let i = $index; let last = $last) {
              <div [ngClass]="{'widen': isOpened[i], 'last': last}" class="article-item relative">
                <div class="vis_part">
                  <div (click)="isOpened[i] = !isOpened[i]" class="art_img_"></div>
                  <div (click)="playTrack(track); $event.stopPropagation();" class="art_img">
                    @if(true) {
                    <img src="assets/images/icons/play_lst.svg" alt="play">
                    } @else {
                    <img src="assets/images/icons/pause_lst.svg" alt="pause">
                    }
                  </div>
                  <div (click)="isOpened[i] = !isOpened[i]" class="flex justify-between sp_width">
                    @if(track){
                    <div class="titl_w w-full">
                      <div class="article-title" (click)="openLecture(track)">
                        {{track.title}}
                      </div>
                      <div class="article-category">
                        {{ track.authorName || track.author }}</div>
                      <div class="flex items-center icons_w b_p">
                        <div class="flex items-center">
                          <div class="icon-wrap cal_w">
                            <svg width="22" height="24" viewBox="0 0 22 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M6.41826 5.02326C5.96059 5.02326 5.58105 4.64372 5.58105 4.18605V0.837209C5.58105 0.379535 5.96059 0 6.41826 0C6.87594 0 7.25547 0.379535 7.25547 0.837209V4.18605C7.25547 4.64372 6.87594 5.02326 6.41826 5.02326Z"
                                fill="var(--text-color)" />
                              <path
                                d="M15.3489 5.02326C14.8913 5.02326 14.5117 4.64372 14.5117 4.18605V0.837209C14.5117 0.379535 14.8913 0 15.3489 0C15.8066 0 16.1861 0.379535 16.1861 0.837209V4.18605C16.1861 4.64372 15.8066 5.02326 15.3489 5.02326Z"
                                fill="var(--text-color)" />
                              <path
                                d="M6.97663 14.7908C6.83151 14.7908 6.6864 14.7573 6.55244 14.7015C6.40733 14.6457 6.2957 14.5676 6.18407 14.4671C5.98314 14.255 5.86035 13.9759 5.86035 13.6745C5.86035 13.5294 5.89384 13.3843 5.94965 13.2503C6.00547 13.1164 6.08361 12.9936 6.18407 12.882C6.2957 12.7815 6.40733 12.7034 6.55244 12.6476C6.9543 12.4801 7.45663 12.5694 7.76919 12.882C7.97012 13.0941 8.09291 13.3843 8.09291 13.6745C8.09291 13.7415 8.08175 13.8196 8.07058 13.8978C8.05942 13.9648 8.0371 14.0317 8.00361 14.0987C7.98128 14.1657 7.94779 14.2327 7.90314 14.2996C7.86965 14.3555 7.81384 14.4113 7.76919 14.4671C7.5571 14.668 7.26686 14.7908 6.97663 14.7908Z"
                                fill="var(--text-color)" />
                              <path
                                d="M10.8839 14.7906C10.7387 14.7906 10.5936 14.7572 10.4597 14.7013C10.3146 14.6455 10.2029 14.5674 10.0913 14.4669C9.89037 14.2548 9.76758 13.9758 9.76758 13.6744C9.76758 13.5292 9.80107 13.3841 9.85688 13.2502C9.91269 13.1162 9.99083 12.9934 10.0913 12.8818C10.2029 12.7813 10.3146 12.7032 10.4597 12.6474C10.8615 12.4688 11.3639 12.5692 11.6764 12.8818C11.8773 13.0939 12.0001 13.3841 12.0001 13.6744C12.0001 13.7413 11.989 13.8195 11.9778 13.8976C11.9666 13.9646 11.9443 14.0316 11.9108 14.0986C11.8885 14.1655 11.855 14.2325 11.8104 14.2995C11.7769 14.3553 11.7211 14.4111 11.6764 14.4669C11.4643 14.6679 11.1741 14.7906 10.8839 14.7906Z"
                                fill="var(--text-color)" />
                              <path
                                d="M14.7911 14.7906C14.646 14.7906 14.5009 14.7572 14.3669 14.7013C14.2218 14.6455 14.1102 14.5674 13.9985 14.4669C13.9539 14.4111 13.9092 14.3553 13.8646 14.2995C13.8199 14.2325 13.7864 14.1655 13.7641 14.0986C13.7306 14.0316 13.7083 13.9646 13.6971 13.8976C13.686 13.8195 13.6748 13.7413 13.6748 13.6744C13.6748 13.3841 13.7976 13.0939 13.9985 12.8818C14.1102 12.7813 14.2218 12.7032 14.3669 12.6474C14.7799 12.4688 15.2711 12.5692 15.5836 12.8818C15.7846 13.0939 15.9074 13.3841 15.9074 13.6744C15.9074 13.7413 15.8962 13.8195 15.885 13.8976C15.8739 13.9646 15.8515 14.0316 15.8181 14.0986C15.7957 14.1655 15.7622 14.2325 15.7176 14.2995C15.6841 14.3553 15.6283 14.4111 15.5836 14.4669C15.3715 14.6679 15.0813 14.7906 14.7911 14.7906Z"
                                fill="var(--text-color)" />
                              <path
                                d="M6.97663 18.6976C6.83151 18.6976 6.6864 18.6642 6.55244 18.6084C6.41849 18.5526 6.2957 18.4744 6.18407 18.3739C5.98314 18.1618 5.86035 17.8716 5.86035 17.5813C5.86035 17.4362 5.89384 17.2911 5.94965 17.1572C6.00547 17.012 6.08361 16.8894 6.18407 16.7889C6.5971 16.3759 7.35617 16.3759 7.76919 16.7889C7.97012 17.001 8.09291 17.2911 8.09291 17.5813C8.09291 17.8716 7.97012 18.1618 7.76919 18.3739C7.5571 18.5748 7.26686 18.6976 6.97663 18.6976Z"
                                fill="var(--text-color)" />
                              <path
                                d="M10.8839 18.6976C10.5936 18.6976 10.3034 18.5748 10.0913 18.3739C9.89037 18.1618 9.76758 17.8716 9.76758 17.5813C9.76758 17.4362 9.80107 17.2911 9.85688 17.1572C9.91269 17.012 9.99083 16.8894 10.0913 16.7889C10.5043 16.3759 11.2634 16.3759 11.6764 16.7889C11.7769 16.8894 11.855 17.012 11.9108 17.1572C11.9666 17.2911 12.0001 17.4362 12.0001 17.5813C12.0001 17.8716 11.8773 18.1618 11.6764 18.3739C11.4643 18.5748 11.1741 18.6976 10.8839 18.6976Z"
                                fill="var(--text-color)" />
                              <path
                                d="M14.7911 18.6975C14.5009 18.6975 14.2106 18.5747 13.9985 18.3738C13.8981 18.2733 13.8199 18.1506 13.7641 18.0054C13.7083 17.8715 13.6748 17.7264 13.6748 17.5813C13.6748 17.4361 13.7083 17.291 13.7641 17.1571C13.8199 17.0119 13.8981 16.8892 13.9985 16.7887C14.2553 16.5319 14.646 16.4092 15.0032 16.4873C15.0813 16.4985 15.1483 16.5208 15.2153 16.5543C15.2822 16.5766 15.3492 16.6101 15.4162 16.6547C15.472 16.6882 15.5278 16.744 15.5836 16.7887C15.7846 17.0008 15.9074 17.291 15.9074 17.5813C15.9074 17.8715 15.7846 18.1617 15.5836 18.3738C15.3715 18.5747 15.0813 18.6975 14.7911 18.6975Z"
                                fill="var(--text-color)" />
                              <path
                                d="M20.3716 9.5886H1.39483C0.937152 9.5886 0.557617 9.20907 0.557617 8.75139C0.557617 8.29372 0.937152 7.91418 1.39483 7.91418H20.3716C20.8292 7.91418 21.2088 8.29372 21.2088 8.75139C21.2088 9.20907 20.8292 9.5886 20.3716 9.5886Z"
                                fill="var(--text-color)" />
                              <path
                                d="M15.3488 24H6.4186C2.34419 24 0 21.6558 0 17.5814V8.09304C0 4.01862 2.34419 1.67444 6.4186 1.67444H15.3488C19.4233 1.67444 21.7674 4.01862 21.7674 8.09304V17.5814C21.7674 21.6558 19.4233 24 15.3488 24ZM6.4186 3.34886C3.22605 3.34886 1.67442 4.90049 1.67442 8.09304V17.5814C1.67442 20.774 3.22605 22.3256 6.4186 22.3256H15.3488C18.5414 22.3256 20.093 20.774 20.093 17.5814V8.09304C20.093 4.90049 18.5414 3.34886 15.3488 3.34886H6.4186Z"
                                fill="var(--text-color)" />
                            </svg>
                          </div>
                          <span class="ml-2 text-color no_wrap">
                            {{ track.date }}
                          </span>
                        </div>
                        <div class="audio-views flex items-center ml-[30px]">
                          <img class="mr-2" src="assets/images/icons/chck.svg" alt="">
                          <span class="ml-1 text-color no_wrap">
                            прослушана {{track.listened_count}} раз (a)
                          </span>
                        </div>
                      </div>
                    </div>
                    }
                    <div class="actions_w" (click)="showMenu(i, $event)" [class.show_menu]="show_menu[i]">
                      <div class="flex items-center cursor-pointer icons_w show_md">
                        <div class="icon-wrap star_w no_h">
                          <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="3.27778" cy="3.27778" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                            <circle cx="3.27778" cy="11.2499" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                            <circle cx="3.27778" cy="19.2221" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                          </svg>
                        </div>
                      </div>
                      <div class="md_chg">
                        <div title="Добавить в очередь" class="flex items-center cursor-pointer icons_w fav_hov"
                          [ngClass]="{'in-favourites': track.id | isInPlaylist : playlists}"
                          (click)="addToPlaylist(track); $event.stopPropagation();">
                          <div class="icon-wrap">
                            <app-list-icon></app-list-icon>
                          </div>
                          <p class="show_p_md">
                            добавить в очередь
                          </p>
                          <div class="on_hov">
                            добавить в очередь
                          </div>
                        </div>
                        <div class="flex items-center cursor-pointer icons_w fav_hov"
                          [ngClass]="{'in-favourites': track.id | isInPlaylist : playlists}"
                          (click)="showPlaylistDialog(track.id); $event.stopPropagation();">
                          <div class="icon-wrap star_w">
                            <app-favorites-icon></app-favorites-icon>
                          </div>
                          <p class="show_p_md">
                            добавить в избранное
                          </p>
                          <div class="on_hov">
                            добавить в избранное
                          </div>
                        </div>
                        <div class="flex items-center cursor-pointer icons_w shr_hov"
                          (click)="share(track); $event.stopPropagation();">
                          <div class="icon-wrap share_w">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                                fill="var(--font-color1)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            поделиться
                          </p>
                          <div class="on_hov">
                            поделиться
                          </div>
                        </div>
                        <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': track.liked}"
                          (click)="like(track.id); $event.stopPropagation();">
                          <div class="icon-wrap like_w">
                            <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                                fill="var(--font-color1)" />
                            </svg>
                            <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                                fill="var(--text-color)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            мне нравится
                          </p>
                          <span class="ml-2 text-color default_">
                            {{track.likes}}
                          </span>
                          <div class="on_hov">
                            мне нравится
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="flex bs_wrapper_">
                    @if(track.text_link) {
                    <p (click)="openLecture(track, 'text')" class="btn_item_wrapper">
                      <span class="btn_item_">Читать</span>
                    </p>
                    }
                    @if(track.youtube) {
                    <p (click)="openLecture(track, 'video')" class="btn_item_wrapper">
                      <span class="btn_item_">Смотреть</span>
                    </p>
                    }
                  </div>
                </div>
                <div class="flex bs_wrapper_ vis_md">
                  @if(track.text_link) {
                  <p (click)="openLecture(track, 'text')" class="btn_item_wrapper">
                    <span class="btn_item_">Читать</span>
                  </p>
                  }
                  @if(track.youtube) {
                  <p (click)="openLecture(track, 'video')" class="btn_item_wrapper">
                    <span class="btn_item_">Смотреть</span>
                  </p>
                  }
                </div>
                <div class="invis_part">

                  <div class="flex">
                    <div class="flex flex-col icons_w b_p">
                      <div class="tr_descr">{{ track.description }}</div>
                      <span class="mt-u_ text-color">Длительность: {{Math.ceil(track.duration / 60)}} мин.</span>
                    </div>
                  </div>
                </div>
              </div>
              }
              <div class="buttn_catg" *ngIf="filters.page < audioService.totalPages && data.length">
                <button class="load-more-button" (click)="nextPage()" [disabled]="false">
                  <span *ngIf="true">Загрузить еще</span>
                  <span *ngIf="false">Загрузка...</span>
                </button>
              </div>
            </div>
          </ng-container>
        </ng-container>
        <!-- <div class="flex p-2">
          <button class="color" (click)="previousPage()" [disabled]="(filters.page) === 1">Previous</button>
          <span>Page {{ filters.page }} of {{ audioService.total }}</span>
          <button class="color" (click)="nextPage()" [disabled]="filters.page === (audioService.total)">Next</button>
        </div> -->
      </div>
    </div>
  </div>
</div>
<playlist-dialog [showPlaylist]="showPlaylist" [selectedTrackId]="selectedTrackId"
  (playlistClosed)="playlistClosed($event)" (playlistSelected)="playlistSelected($event)">
</playlist-dialog>