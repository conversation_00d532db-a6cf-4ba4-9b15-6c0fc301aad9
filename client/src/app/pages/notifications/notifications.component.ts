import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component"
import { environment } from "@/env/environment"
import { NotificationsService } from '@/services/notifications.service'
import { ProfileService } from '@/services/profile.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, NgOptimizedImage } from "@angular/common"
import { Component, inject, OnInit, ViewEncapsulation } from "@angular/core"
import { Router } from '@angular/router'
import { TranslocoService } from "@jsverse/transloco"

@Component({
    selector: 'app-notifications',
    standalone: true,
    imports: [
      CommonModule,
      BreadcrumbComponent,
      NgOptimizedImage
    ],
    encapsulation: ViewEncapsulation.None,
    templateUrl: './notifications.component.html',
    styleUrl: './notifications.component.scss'
  })
  export class NotificationsComponent implements OnInit {
    protected readonly environment = environment;
    notificationsService = inject(NotificationsService);
    translocoService = inject(TranslocoService)
    profileService = inject(ProfileService);
    toasterService = inject(ToasterService);
    router = inject(Router)
    items: any[] = []
   
  
    ngOnInit() {
        this.notificationsService.getAll().subscribe(
            {
                next: (res: any) => {
                    this.items = res;

                    if(this.profileService.profile) {
                        this.profileService.profile!.unreadNotificationsCount = 0
                    }
                },
                error: (err) => {
                    console.error('Error loading notifications:', err);
                }
            }
        );
    }

    deleteNotification(id: number, event: Event) {
        event.stopPropagation();

        this.notificationsService.delete(id).subscribe({
            next: () => {
                const index = this.items.findIndex(item => item.id === id);
                if (index > -1) {
                    this.items.splice(index, 1);
                }
                this.toasterService.showToast('Уведомление удалено', 'success', 'bottom-middle', 3000);
            },
            error: (err) => {
                this.toasterService.showToast('Ошибка при удалении уведомления', 'error', 'bottom-middle', 3000);
            }
        });
    }
}