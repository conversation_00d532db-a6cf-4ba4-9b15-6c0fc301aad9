<breadcrumb></breadcrumb>

<div class="container">
  <div class="news-page">
    <div class="page-header">
      <h1 class="page-title">Уведомления</h1>
    </div>

    <div class="advertisements-list">
      @if (items.length > 0) {
        <div class="ads-grid">
          @for (item of items; track item.id) {
            <article class="ad-card" (click)="router.navigate([item.link])">
              <!-- <div class="ad-image-container">
                 <div class="ad-image-placeholder">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z" fill="currentColor"/>
                    </svg>
                  </div>
              </div> -->

              <button class="delete-notification-btn"
                      (click)="deleteNotification(item.id, $event)"
                      title="Удалить уведомление">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>

              <div class="ad-content">
                <h3 class="ad-title">{{translocoService.translate(item.type)}}</h3>
                <p class="ad-description">{{ item.title }}</p>
                <div class="ad-meta">
                  <time class="ad-date">{{ item.createdAt | date:'mediumDate' }}</time>
                  <span class="ad-link-indicator">Читать далее →</span>
                </div>
              </div>
            </article>
          }
        </div>
      } @else {
        <div class="no-items">
          <p>Уведомлений пока нет</p>
        </div>
      }
    </div>
  </div>
</div>


