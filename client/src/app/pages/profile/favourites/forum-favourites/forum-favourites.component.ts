import {Component, ElementRef, inject, ViewChild} from "@angular/core";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {ToasterService} from "@/services/toaster.service";
import {Router, RouterLink} from "@angular/router";
import {ForumService} from "@/services/forum.service";
import {ProfileService} from "@/services/profile.service";
import moment from "moment";
import 'moment/locale/ru';
moment.locale('ru');

@Component({
  selector: 'ForumFavourites',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage, RouterLink],
  templateUrl: './forum-favourites.component.html',
  styleUrl: '../favourites.component.scss'
})
export class ForumFavouritesComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  toasterService = inject(ToasterService);
  forumService = inject(ForumService);
  profileService = inject(ProfileService);
  router = inject(Router);
  message: string = "";
  selectedDropdElement: any = null;
  forumActions = [
    'удалить',
    'поделиться',
  ];

  items: any = []
  page: number = 1;
  totalPages = 1;
  itemsPerPage = 5;

  ngOnInit() {
    this.get()
  }

  get() {
    this.forumService.getFavourites().subscribe((res: any) => {
      this.items = [...this.items, ...res.topicFavorites, ...res.topicCommentFavorites];
      this.totalPages = Math.ceil(this.items.length / this.itemsPerPage);
    })
  }

  get forumFavouritePagination() {
    return this.items.slice(0, this.page*this.itemsPerPage)
  }

  get formatDate() {
    return (date: any) => moment(date).calendar()
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  redirect(item: any) {
    this.router.navigateByUrl('/ru/categories/' + item.category.id + '/' + item.slug);
  }

  closeMobileActionSelect() {
    this.selectedDropdElement = null;
  }

  showMobileActionOptions(quote: any) {
    this.selectedDropdElement = quote;
  }

  onClickMobileAction() {
    this.closeMobileActionSelect();
  }

  share(content: any) {
    let url = this.environment.baseUrl + '/' + content.lang + '/categories/' + content.category.id + '/' + content.slug;
    navigator.clipboard.writeText(url)
      .then(() => {
        this.toasterService.showToast('Ссылка скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
      })
      .catch(err => {
        console.error('Не удалось скопировать ссылку: ', err);
        this.toasterService.showToast('Не удалось скопировать ссылку', 'error', 'bottom-middle', 3000);
      });
  }

  favoriteTopic(id: number) {
    this.openConfirmationDialog('Удалить из избранного?').then((confirmed) => {
      if (confirmed) {
        this.forumService.favoriteTopic(id).subscribe({
          next: () => {
            this.profileService.getProfile().subscribe()
            const index = this.items.findIndex((e: any) => e?.topic?.id === id);
            if(index > -1) this.items.splice(index, 1);
            this.toasterService.showToast('Форум удален из избранного!', 'success', 'bottom-middle', 3000);
          },
          error: () => {
            this.profileService.getProfile().subscribe()
            this.openModal('Ошибка удаления, попробуйте еще раз')
          }
        });
      }
    });
  }

  favoriteComment(id: number) {
    this.openConfirmationDialog('Удалить комментарий из избранного?').then((confirmed) => {
      if (confirmed) {
        this.forumService.favoriteComment(id).subscribe({
          next: () => {
            this.profileService.getProfile().subscribe()
            const index = this.items.findIndex((e: any) => e?.topicComment?.id === id);
            if(index > -1) this.items.splice(index, 1);
            this.toasterService.showToast('Комментарий удален из избранного!', 'success', 'bottom-middle', 3000);
          },
          error: () => {
            this.profileService.getProfile().subscribe()
            this.openModal('Ошибка удаления, попробуйте еще раз')
          }
        });
      }
    });
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  copyTopic(item: any, commentId = null) {
    let url = this.environment.baseUrl + '/ru/forum/topic/' + item + (commentId ? '?commentId=' + commentId : '')
    navigator.clipboard.writeText(url)
      .then(() => {
        this.toasterService.showToast('Текст скопирован в буфер обмена!', 'success', 'bottom-middle', 3000);
      })
      .catch(err => {
        console.error('Не удалось скопировать текст: ', err);
        this.toasterService.showToast('Не удалось скопировать текст', 'error', 'bottom-middle', 3000);
      });
  }

  like(topic: any) {
    if(topic?.topicComment) {
      this.forumService.likeComment(topic.topicComment.id).subscribe(() => {
        topic.topicComment.liked = !topic.topicComment.liked
        if(!topic.topicComment.liked) {
          topic.topicComment.likes--
        } else {
          topic.topicComment.likes++
        }
      })
      return
    }

    this.forumService.likeTopic(topic.topic.id).subscribe(() => {
      topic.topic.liked = !topic.topic.liked;
      if(!topic.topic.liked) {
        topic.topic.likes--
      } else {
        topic.topic.likes++
      }
    })
  }

  protected readonly environment = environment;
}
