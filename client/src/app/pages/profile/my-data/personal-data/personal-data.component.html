<div class="flex flex-col items-center w-full">
  <form [formGroup]="form" class="profile-form flex flex-col relative">
    <div class="avatar flex justify-center items-center" (click)="triggerFileInput()">
      <img class="profile-avatar" *ngIf="form.value.avatar; else emptyAvatar"
        [src]="environment.serverUrl + '/upload/' + form.value.avatar!.name" alt="">
      <ng-template #emptyAvatar>
        <div class="empty-avatar"></div>
      </ng-template>
      <input #fileInput class="hidden" type="file" accept="image/*" (change)="uploadAvatar($event)">
    </div>
    <div class="flex flex-col gap-1">
      <label>Имя</label>
      <div class="field-wrapper">
        <input type="text" formControlName="firstName">
        <!-- <span class="sufix">*</span> -->
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>Фамилия</label>
      <div class="field-wrapper">
        <input type="text" formControlName="lastName">
        <!-- <span class="sufix">*</span> -->
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>Отчество</label>
      <div class="field-wrapper">
        <input type="text" formControlName="middleName">
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>Духовное имя (если есть)</label>
      <div class="field-wrapper">
        <input type="text" formControlName="spiritualName">
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>E-mail</label>
      <div class="field-wrapper">
        <input type="text" formControlName="email">
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>Телефон</label>
      <div class="field-wrapper">
        <input type="text" formControlName="phone">
      </div>
    </div>
    <div class="flex flex-col gap-1">
      <label>Telegram</label>
      <div class="field-wrapper">
        <input type="text" formControlName="telegram">
      </div>
    </div>
    <button type="submit" class="save-btn" (click)="onSubmit()">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="save-btn-label">Сохранить</div>
    </button>
  </form>
  <div *ngIf="authService.token()" class="button_img side">
    <a (click)="$event.preventDefault(); logout()" class="exit-button">
      Выход
    </a>
  </div>
</div>
