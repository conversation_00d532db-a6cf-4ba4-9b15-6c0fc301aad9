import {Component, inject} from '@angular/core';
import {Router, ActivatedRoute} from "@angular/router";
import {CommonModule} from '@angular/common';
import {PersonalDataComponent} from "@/pages/profile/my-data/personal-data/personal-data.component";
import {QuestionnaireDataComponent} from "@/pages/profile/my-data/questionnaire-data/questionnaire-data.component";

@Component({
  selector: 'app-my-data',
  standalone: true,
  imports: [
    CommonModule,
    PersonalDataComponent,
    QuestionnaireDataComponent
  ],
  templateUrl: './my-data.component.html',
  styleUrl: './my-data.component.scss'
})
export class MyDataComponent {
  router = inject(Router);
  route = inject(ActivatedRoute);
  
  tabsList = [
    {
      label: 'Личные данные',
      value: 'personal',
    },
    {
      label: 'Анкета',
      value: 'questionnaire',
    },
  ];
  selectedTab = this.tabsList[0];

  ngOnInit(): void {
    // Подписываемся на изменения параметров роута для определения активного саб-таба
    this.route.params.subscribe(params => {
      const subtab = params['subtab'];
      if (subtab) {
        const tab = this.tabsList.find(t => t.value === subtab);
        if (tab) {
          this.selectedTab = tab;
        } else {
          // Если саб-таб не найден, перенаправляем на первый таб
          this.navigateToSubTab(this.tabsList[0].value);
        }
      } else {
        // Если нет параметра subtab, перенаправляем на первый саб-таб
        this.navigateToSubTab(this.tabsList[0].value);
      }
    });
  }

  selectSubTab(tab: any): void {
    this.navigateToSubTab(tab.value);
  }

  private navigateToSubTab(subTabValue: string): void {
    this.router.navigate(['/ru/profile/my-data', subTabValue]);
  }

  showNext() {
    const currentIndex = this.tabsList.findIndex(tab => tab.value === this.selectedTab.value);
    if (currentIndex < this.tabsList.length - 1) {
      this.navigateToSubTab(this.tabsList[currentIndex + 1].value);
    }
  }

  showPrev() {
    const currentIndex = this.tabsList.findIndex(tab => tab.value === this.selectedTab.value);
    if (currentIndex > 0) {
      this.navigateToSubTab(this.tabsList[currentIndex - 1].value);
    }
  }
}
