@import url(../audio-gallery/audio-gallery.component.scss);

breadcrumb {
  margin: 60px 0 30px 0;
}

.cat-f-item_wrap {
  position: relative;
  display: flex;
  border-radius: 20px;
  border: 1px solid var(--book_about);
  padding: 30px 30px 60px 30px;
  margin-bottom: 61px;
  flex: 1 1 570px;
  max-width: 48.7%;
  box-sizing: border-box;
}

.middle_stripe {
  padding: 0 34px;
}

.cat-f-item {
  width: 100%;
}

.confirm-btn {
  width: 234px;
  height: 50px;
  padding: 0 !important;
  background: transparent;
  position: absolute;
  bottom: -23px;
  right: 50%;
  left: 50%;
  transform: translate(-50%, 0);

  &:focus {
    outline: none;
  }

  &.focus-visible {
    outline: none;
  }

  .btn-backdrop-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }

  .confirm-btn-label {
    position: relative;
    margin: 0 auto;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    padding: 14px 25px;
    color: var(--font-color1);
    z-index: 12;
  }
}

.cat-badge {
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 28px;
  color: var(--text-color);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 5px;
  cursor: pointer;
}

.cat_descrpn {
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  color: var(--font-color1);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.save-btn.mg-auto {
  width: 318px;
  height: 50px;
}

.tw_betw {
  display: flex;
  justify-content: space-between;
}

.sd_cont {
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 21px;
  color: var(--text-color);
}

.cat-f_wrap {
  display: flex;
  flex-wrap: wrap;
}

.ct_line {
  border-bottom: 1px solid var(--book_about);
  width: 100%;
  margin-top: 21px;
  margin-bottom: 17px;
}

.cat-p-item {
  margin-right: 30px;
  min-width: 90px;

  img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    object-fit: cover;
  }
}

.wrapper_line {
  max-width: 1170px;
}

.cat_wrap {
  max-width: 1170px;
}

.cat-f-item_wrap:nth-child(odd) {
  margin-right: auto;
}

.none-dt {
  display: none;
}

@media (max-width: 940px) {
  .cat-p-item {
    margin-right: 20px;
    min-width: 60px;

    img {
      width: 60px;
      height: 60px;
    }
  }

  .sd_cont {
    font-size: 15px;
    line-height: 18px;
  }

  .confirm-btn .confirm-btn-label {
    font-size: 18px;
  }

  .cat-badge {
    font-size: 18px;
    line-height: 24px;
  }

  .cat_descrpn {
    font-size: 15px;
    line-height: 19px;
  }

  .cat-f-item_wrap {
    padding: 20px 20px 50px 20px;
    margin-bottom: 45px;
  }
}

@media (max-width: 768px) {
  .cat-f-item_wrap {
    flex-direction: column;
  }

  breadcrumb {
    margin: 44px 0 30px 0;
  }

  .none-md {
    display: none;
  }

  .cat-p-item {
    display: flex;
    margin-bottom: 20px;
    margin-right: 0;
    align-items: center;
  }

  .cat_descrpn {
    margin-bottom: 40px;
  }

  .none-dt {
    display: block;
    margin-left: 20px;
  }
}

@media (max-width: 650px) {
  .confirm-btn {
    width: 210px;
    height: 45px;
  }

  .confirm-btn .confirm-btn-label {
    font-size: 17px;
  }

  .save-btn.mg-auto:last-of-type {
    width: 260px;
    margin: 30px 0 0 0 !important;
  }
}

@media (max-width: 600px) {
  breadcrumb {
    margin: 33px 0 28px 0;
  }
}

@media (max-width: 540px) {
  .save-btn .save-btn-label {
    font-size: 17px;
  }
}

@media (max-width: 570px) {
  .cat-f_wrap {
    margin-top: 40px;
  }

  .middle_stripe {
    padding: 0 17px;
  }

  .cat-f_wrap {
    flex-wrap: unset;
    flex-direction: column;
  }

  .cat-f-item_wrap:nth-child(odd) {
    margin-right: 0;
  }

  .cat-f-item_wrap {
    padding: 14px 19px 40px 19px;
    margin-bottom: 40px;
    max-width: 100%;
    flex: unset;
  }
}

@media (max-width: 450px) {
  .save-btn .save-btn-label {
    font-size: 17px;
  }

  .none-dt {
    margin-left: 0;
    margin-bottom: 0;
    margin-top: 10px;
  }

  .cat-item-header {
    text-align: center;
  }

  .cat-p-item {
    flex-direction: column;
    margin-bottom: 5px;
  }

  .cat_descrpn {
    margin-bottom: 0;
  }

  .ct_line {
    margin-top: 12px;
    margin-bottom: 15px;
  }
}