@import '../../../assets/styles/new-typography';
@import '../../../assets/styles/new-palette';

:host {
    background-color: main(50);
}
header {
    height: 108px;
    position: fixed;
    top: 0;
    width: 100%;
    background-color: transparent;
    padding: 16px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    &.scrolled {
        background-color: #FFEBBF;
        // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        transition: background-color 0.25s ease-in-out;
    }
    .left-actions {
        display: flex;
        align-items: center;
        gap: 24px;
    }
    .right-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
    }
}
.main-banner {
    width: 100%;
    height: 400px;
    background: url(../../../assets/images/main-v2/iryna_it_cinematic_wide_angle_shot_of_a_beautiful_Hindu_temple__3cd826ee-783a-44af-a6b8-8227a4935d7b\ 1.webp) no-repeat left center;
    background-size: cover;
    height: 1024px;
    padding-bottom: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    h1 {
        @include h1;
        color: main(600);
        text-align: center;
        padding-top: 158px;
        margin-bottom: 40px;
    }
    h3 {
        @include h3;
        color: main(600);
        text-align: center;
        margin-bottom: 60px;
    }
    .logo {
        margin: auto;

    }
    .call-to-action-btn {
        background-image: url(../../../assets/images/main-v2/call-to-action-button.webp);
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        color: main(50);
        @include button-1;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 256px;
        height: 56px;
        // box-shadow: 0px 3px 20.2px 0px #160E02C9;
        
        .btn-label {
            // border-radius: 100px;
            // backdrop-filter: blur(15.399999618530273px);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            width: 220px;
            height: 52px;
            // margin-bottom: 3px;
        }
    }
}
.main-contetnt-wrapper {
    max-width: 1440px;
    width: 100%;
    margin: 0 auto;
    padding: 110px 0;
    overflow-x: hidden;
    .carousel-header {
        max-width: 1200px;
        margin: 0 auto 100px auto;
        color: main(600);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px;
        .carousel-title {
            @include h2;
            line-height: 100%;
        }
        a {
            @include button-1;
            color: main(700);
        }
    }
}

.join-cards-wrapper {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

section {
    margin-bottom: 260px;
}

footer {
    background-image: url(../../../assets/images/main-v2/Footer.webp);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 846px;
    position: relative;
    padding: 107px 34px 36px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    @include caption-2;
    color: main(100);
    .logo {
        width: 120px;
        height: 120px;
        max-width: 100%;
    }
    .footer-head-mask {
        width: 100%;
        height: 120px;
        background: linear-gradient(180deg, rgba(255, 235, 191, 0) 0%, #FFEBBF 100%);
        background-image: url(../../../assets/images/main-v2/footer-mask-xl.webp);
        background-position: top;
        background-size: cover;
        background-repeat: no-repeat;
        position: absolute;
        left: 0;
        top: -1px;
        z-index: 2;
        @media (max-width: 1500px) {
            background-image: url(../../../assets/images/main-v2/footer-mask-lg.webp);
        }
        @media (max-width: 920px) {
            background-image: url(../../../assets/images/main-v2/footer-mask-md.webp);
        }
        @media (max-width: 560px) {
            background-image: url(../../../assets/images/main-v2/footer-mask-sm.webp);
        }
    }
    .desktop-title{
        display: block;
        color: main(100);
        max-width: 450px;
        .title {
            @include button-1;
            margin-bottom: 32px;
        }
        .subtitle {
            @include body-3;
        }
        @media (max-width: 920px) {
            display: none;
        }
    }
    .mobile-title{
        display: none;
        color: main(700);
        @media (max-width: 920px) {
            display: block;
        }
        .title {
            @include subtitle-1;
            margin: 32px 0;
            text-align: center
        }
        .subtitle {
            @include body-3;
            text-align: center
        }
    }
    .label {
        @include body-2;
        color: main(300);
    }
    
    .privacy-policy {
        @include button-4;
        color: main(100);
        &-link {
            cursor: pointer;
        }
    }

    .links-section {
        display: flex;
        gap: 50px;
        .left-content {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .shared-section {
                display: flex;
                flex-direction: column;
                gap: 40px;
            }
        }

        .right-content {
            display: flex;
            gap: 30px;
            .links-col {
                display: flex;
                flex-direction: column;
                gap: 30px;
            }
        }
    }
}

.projects-and-ministry-section {
    .projects-tabs {
        margin: 0 15px;
        display: flex;
        width: 100%;
        justify-content: space-between;
        position: relative;
        margin-bottom: 44px;
        &:after {
            width: 100%;
            content: '';
            height: 2px;
            background: main(200);
            position: absolute;
            bottom: 0;
            left: 0;
        }
        .tab-item {
            height: 96px;
            padding: 32px 0;
            color: main(600);
            @include subtitle-1;
            cursor: pointer;
            opacity: 0.5;
            transition: all 0.2s ease-in-out;
            &.active {
                opacity: 1;
                border-bottom: 2px solid main(400);
                position: sticky;
                z-index: 2;
            }
        }
    }
    .selected-tab-content {
        display: flex;
        gap: 24px;
        justify-content: space-between;
        max-width: 81%;
        margin: 0 auto;
        .tab-text-content {
            padding: 45px 0;
            .tab-date {
                @include caption-2;
                color: main(500);
                margin-bottom: 30px
            }
            .tab-description {
                @include body-1;
                color: main(600);
                margin-bottom: 50px;
                max-width: 527px;
            }
            .primaty-button {
                background: url(../../../assets/images/main-v2/primary-button.webp) no-repeat center;
                cursor: pointer;
                width: 165px;
                height: 45px;
                display: flex;
                align-items: center;
                justify-content: center;
                @include button-2;
                color: main(600);
            }
        }
        .tab-image {
            width: 453px;
            height: fit-content;
            // padding: 1px;
            position: relative;
            .mask {
                width: 100%;
                height: 461px;
                background: url(../../../assets/images/main-v2/tab-mask.webp) no-repeat center;
                position: sticky;
                z-index: 2;
            }
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 8px;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }
}