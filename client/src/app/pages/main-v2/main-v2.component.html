<header [class.scrolled]="isScrolled">
  <div class="left-actions">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 12L20 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M4 18L20 18" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M4 6L20 6" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

    <a href="">Главная</a>
    <a href="">О традиции</a>
    <a href="">Обучение</a>
    <a href="">Практика</a>
    <a href="">Библиотека</a>
    <a href="">События</a>
  </div>
  <div class="right-actions">
    <a href="">Форум</a>
    <a href="">Поддержать</a>
    <a href="">Eng ^</a>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 2L12 4" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 20L12 22" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M4.92969 4.92969L6.33969 6.33969" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M17.6562 17.6602L19.0662 19.0702" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M2 12L4 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M20 12L22 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M6.33969 17.6602L4.92969 19.0702" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M19.0662 4.92969L17.6562 6.33969" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20.9963 21.0002L16.6562 16.6602" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>


  </div>
</header>

<div class="main-banner">
  <div>
    <h1>
      Всемирная Община Санатана Дхармы
    </h1>
    <h3>
      Да распространится Дхарма во всех мирах
    </h3>
    <img class="logo" src="../../../assets/images/main-v2/om_big 2.webp" alt="logo">
  </div>
  <div class="call-to-action-btn">
    <div class="btn-label">
      Начать свой путь
    </div>
  </div>
</div>

<div class="main-contetnt-wrapper">
  <section class="chronological-section">
    <div class="carousel-header">
      <div class="carousel-title">
        Мероприятия и он-лайн ритриты
      </div>
      <a class="cursor-pointer" href="">Смотреть больше</a>
    </div>
    <app-carousel-v2 [items]="events" [itemsPerView]="itemsPerView">
      <ng-template let-event>
        <app-chronological-card [value]="event"></app-chronological-card>
      </ng-template>
    </app-carousel-v2>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
  <section class="start-learning-section">
    <div class="carousel-header">
      <div class="carousel-title">
        Начните обучение
      </div>
      <a class="cursor-pointer" href="">Смотреть больше</a>
    </div>
    <app-carousel-v2 [items]="courses" [itemsPerView]="itemsPerView">
      <ng-template let-course>
        <app-accent-card [value]="course"></app-accent-card>
      </ng-template>
    </app-carousel-v2>
  </section>
  <section class="chronological-section">
    <div class="carousel-header">
      <div class="carousel-title mx-auto">
        Библиотека мудости
      </div>
    </div>
    <app-carousel-v2 [items]="events" [itemsPerView]="itemsPerView">
      <ng-template let-event>
        <app-card-with-domes [value]="event"></app-card-with-domes>
      </ng-template>
    </app-carousel-v2>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
  <!-- <app-chronological-card></app-chronological-card> -->
  <!-- <app-accent-card></app-accent-card>
  <app-card-with-dome></app-card-with-dome> -->

  <section class="projects-and-ministry-section">
    <div class="carousel-header flex-col gap-[34px]">
      <div class="carousel-title">
        Проекты и служение
      </div>
      <a class="cursor-pointer" href="">Смотреть больше</a>
    </div>
    <div class="projects-tabs">
      @for (tab of projectTabs; track tab) {
        <div 
          class="tab-item cursor-pointer"
          [class.active]="tab.title === activeProjectTab.title"
          (click)="setActiveProjectTab(tab)"
        >
          {{tab.title}}
        </div>
      }
    </div>
    <div class="selected-tab-content">
      <div class="tab-text-content">
        <div class="tab-date">{{activeProjectTab.date}}</div>
        <div class="tab-description">{{activeProjectTab.description}}</div>
        <div class="primaty-button">Подробнее</div>
      </div>
      <div class="tab-image">
        <div class="mask"></div>
        <img src="../../../assets/images/main-v2/default-pictures/Центр.webp" alt="">
      </div>
    </div>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
  <section class="join-section">
    <div class="carousel-header">
      <div class="carousel-title mx-auto">
        Присоедииться
      </div>
    </div>
    <div class="join-cards-wrapper">
      @for (card of joinCards; track card.name) {
        <app-join-card [value]="card"></app-join-card>
      }
    </div>
    <!-- <app-carousel-v2 [items]="courses" [itemsPerView]="4">
      <ng-template let-course>
        <div class="carousel-item">
          <div class="card">
            <img [src]="course.image" alt="" />
            <h4>{{ course.level }}</h4>
            <p>{{ course.date }}</p>
            <h3>{{ course.title }}</h3>
            <p>{{ course.description }}</p>
          </div>
        </div>
      </ng-template>
    </app-carousel-v2> -->
  </section>
</div>
<footer>
  <div class="footer-head-mask"></div>
  <div class="title-section flex items-center">
    <div class="mobile-title">
      <img class="logo m-auto" src="../../../assets/images/main-v2/om_big 2.webp" alt="logo">
      <div class="title">
        Всемирная Община Санатана Дхармы
      </div>
      <div class="subtitle">
        Всемирная община объединяет людей из многих стран, принадлежащих к Санатана Дхарме (индуизму) и практикующих йогу и медитацию 
        под руководством просветленного Мастера практической адвайты Свами Вишнудевананда Гири Махараджа.
      </div>
    </div>
  </div>
  <div class="links-section">
    <div class="left-content">
      <div class="flex flex-col justify-between h-full">
        <div class="flex justify-between">
          <div class="desktop-title">
            <div class="title">
              Всемирная Община Санатана Дхармы
            </div>
            <div class="subtitle">
              Всемирная община объединяет людей из многих стран, принадлежащих к Санатана Дхарме (индуизму) и практикующих йогу и медитацию 
              под руководством просветленного Мастера практической адвайты Свами Вишнудевананда Гири Махараджа.
            </div>
          </div>
          <div class="shared-section">
            <div class="label">
              Поделиться
            </div>
            <div class="mail"><EMAIL></div>
            <div class="flex gap-[24px]">
              <img src="../../../assets/images/main-v2/telegram.svg" alt="">
              <img src="../../../assets/images/main-v2/youtube.svg" alt="">
              <img src="../../../assets/images/main-v2/vk.svg" alt="">
              <img src="../../../assets/images/main-v2/facebook.svg" alt="">
              <img src="../../../assets/images/main-v2/instagram.svg" alt="">
            </div>
          </div>

        </div>
        <div class="privacy-policy">
          <span>© 2025 Всемирная Община Санатана Дхармы | </span>
          <a class="privacy-policy-link" href="">Политика конфиденциальности</a>
        </div>
      </div>
    </div>
    <div class="right-content">
      <div class="links-col">
        <div class="label">
          Навигация
        </div>
        <a href="">О традиции</a>
        <a href="">Община</a>
        <a href="">События</a>
        <a href="">Библиотека</a>
        <a href="">Ашрамы</a>
      </div>
      <div class="links-col">
        <div class="label">
          Обучение и практика
        </div>
        <a href="">Курсы</a>
        <a href="">Ретриты</a>
        <a href="">Форум</a>
        <a href="">Стать учеником</a>
      </div>
      <div class="links-col">
        <div class="label">
          Информация
        </div>
        <a href="">Новости</a>
        <a href="">Проекты и служение</a>
        <a href="">Пожертвовать</a>
      </div>
    </div>
  </div>
</footer>

<ng-template #footerTitleSection>
  <div class="footer-title-section">
    
  </div>
</ng-template>