import { RenderMode, ServerRoute, PrerenderFallback } from '@angular/ssr';

/**
 * Advanced server routes configuration with dynamic prerendering
 * This configuration demonstrates how to use getPrerenderParams for dynamic content
 */
export const advancedServerRoutes: ServerRoute[] = [
  // Root redirect - prerender for better performance
  { 
    path: '', 
    renderMode: RenderMode.Prerender 
  },
  
  // Language-specific main pages - prerender for all supported languages
  { 
    path: ':lang', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      // Support all languages from app config
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  // Static pages - prerender for better performance and SEO
  { 
    path: ':lang/photo', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  // Photo details - prerender popular photos, fallback to SSR for others
  { 
    path: ':lang/photo/:id', 
    renderMode: RenderMode.Prerender,
    fallback: PrerenderFallback.Server, // Fallback to SSR for non-prerendered photos
    async getPrerenderParams() {
      // Prerender popular photo IDs for all languages
      // In a real application, you could fetch these from your API during build time
      const languages = ['ru', 'en', 'de'];
      const popularPhotoIds = ['1', '2', '3', '4', '5'];
      const params = [];

      for (const lang of languages) {
        for (const id of popularPhotoIds) {
          params.push({ lang, id });
        }
      }

      return params;
    }
  },
  
  { 
    path: ':lang/audiogallery/audiolektsii', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  // Audio lectures - prerender popular lectures, fallback to SSR
  { 
    path: ':lang/audiogallery/audiolektsii/:param', 
    renderMode: RenderMode.Prerender,
    fallback: PrerenderFallback.Server,
    async getPrerenderParams() {
      // Prerender popular lecture parameters for all languages
      // In a real application, you could fetch these from your API during build time
      const languages = ['ru', 'en', 'de'];
      const popularLectures = ['lecture-1', 'lecture-2', 'lecture-3'];
      const params = [];

      for (const lang of languages) {
        for (const param of popularLectures) {
          params.push({ lang, param });
        }
      }

      return params;
    }
  },
  
  { 
    path: ':lang/audiogallery/videolektsii', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  { 
    path: ':lang/library', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  // Library items - prerender popular books, fallback to SSR
  { 
    path: ':lang/library/:code', 
    renderMode: RenderMode.Prerender,
    fallback: PrerenderFallback.Server,
    async getPrerenderParams() {
      // Prerender popular book codes for all languages
      // In a real application, you could fetch these from your API during build time
      const languages = ['ru', 'en', 'de'];
      const popularBooks = ['book-1', 'book-2', 'book-3'];
      const params = [];

      for (const lang of languages) {
        for (const code of popularBooks) {
          params.push({ lang, code });
        }
      }

      return params;
    }
  },
  
  { 
    path: ':lang/categories', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  { 
    path: ':lang/sitemap', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  { 
    path: ':lang/forum', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  { 
    path: ':lang/news', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  { 
    path: ':lang/audiofiles', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  
  { 
    path: ':lang/search', 
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  // User authentication pages - client-side rendering for better UX
  { 
    path: ':lang/signin', 
    renderMode: RenderMode.Client 
  },
  { 
    path: ':lang/signup', 
    renderMode: RenderMode.Client 
  },
  { 
    path: ':lang/forgot', 
    renderMode: RenderMode.Client 
  },

  // User profile pages - client-side rendering (user-specific content)
  { 
    path: ':lang/profile/**', 
    renderMode: RenderMode.Client 
  },

  // Dynamic content pages - server-side rendering for SEO
  { 
    path: ':lang/categories/:id', 
    renderMode: RenderMode.Server 
  },
  
  { 
    path: ':lang/categories/:id/:page', 
    renderMode: RenderMode.Server 
  },
  
  { 
    path: ':lang/forum/:id', 
    renderMode: RenderMode.Server 
  },
  
  { 
    path: ':lang/forum/topic/:id', 
    renderMode: RenderMode.Server 
  },
  
  { 
    path: ':lang/mypage/:id', 
    renderMode: RenderMode.Server 
  },
  
  { 
    path: ':lang/:category/:page', 
    renderMode: RenderMode.Server 
  },
  
  { 
    path: ':lang/anketa', 
    renderMode: RenderMode.Server 
  },

  // 404 page - server-side rendering with proper status code
  { 
    path: '**', 
    renderMode: RenderMode.Server,
    status: 404,
    headers: {
      'Cache-Control': 'no-cache'
    }
  }
];
