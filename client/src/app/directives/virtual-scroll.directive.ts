import {
  Directive,
  ElementRef,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  inject,
  PLATFORM_ID,
  signal,
  computed,
  effect
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { LazyLoadingService } from '@/services/lazy-loading.service';

export interface VirtualScrollItem {
  id: string | number;
  data: any;
  height?: number;
}

export interface VirtualScrollViewport {
  startIndex: number;
  endIndex: number;
  offsetY: number;
  totalHeight: number;
}

@Directive({
  selector: '[appVirtualScroll]',
  standalone: true
})
export class VirtualScrollDirective implements OnInit, OnDestroy {
  private elementRef = inject(ElementRef<HTMLElement>);
  private platformId = inject(PLATFORM_ID);
  private lazyLoadingService = inject(LazyLoadingService);

  // Inputs
  @Input() items: VirtualScrollItem[] = [];
  @Input() itemHeight: number = 50;
  @Input() containerHeight: number = 400;
  @Input() bufferSize: number = 5;
  @Input() scrollDebounceTime: number = 16; // ~60fps

  // Outputs
  @Output() viewportChange = new EventEmitter<VirtualScrollViewport>();
  @Output() scrollEnd = new EventEmitter<void>();

  // Signals for reactive state
  private scrollTop = signal(0);
  private containerSize = signal({ width: 0, height: 0 });

  // Computed viewport
  private viewport = computed(() => {
    const scroll = this.scrollTop();
    const totalItems = this.items.length;
    
    if (totalItems === 0) {
      return {
        startIndex: 0,
        endIndex: 0,
        offsetY: 0,
        totalHeight: 0
      };
    }

    const { startIndex, endIndex, offsetY } = this.lazyLoadingService.calculateVisibleItems(
      scroll,
      this.containerHeight,
      this.itemHeight,
      totalItems,
      this.bufferSize
    );

    return {
      startIndex,
      endIndex,
      offsetY,
      totalHeight: totalItems * this.itemHeight
    };
  });

  private scrollListener?: () => void;
  private resizeObserver?: ResizeObserver;
  private isScrolling = false;
  private scrollEndTimer?: NodeJS.Timeout;

  constructor() {
    // React to viewport changes
    effect(() => {
      const currentViewport = this.viewport();
      this.viewportChange.emit(currentViewport);
    });
  }

  ngOnInit(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    this.setupVirtualScroll();
    this.setupResizeObserver();
  }

  ngOnDestroy(): void {
    this.cleanup();
  }

  private setupVirtualScroll(): void {
    const element = this.elementRef.nativeElement;
    
    // Set up container styles
    element.style.overflowY = 'auto';
    element.style.height = `${this.containerHeight}px`;
    element.style.position = 'relative';

    // Create scroll listener with throttling
    this.scrollListener = this.lazyLoadingService.throttle(
      () => this.handleScroll(),
      this.scrollDebounceTime
    );

    element.addEventListener('scroll', this.scrollListener, { passive: true });
  }

  private setupResizeObserver(): void {
    if (!window.ResizeObserver) return;

    this.resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        this.containerSize.set({ width, height });
        
        // Update container height if it changed
        if (height !== this.containerHeight) {
          this.containerHeight = height;
        }
      }
    });

    this.resizeObserver.observe(this.elementRef.nativeElement);
  }

  private handleScroll(): void {
    const element = this.elementRef.nativeElement;
    this.scrollTop.set(element.scrollTop);

    // Handle scroll start/end detection
    if (!this.isScrolling) {
      this.isScrolling = true;
      element.classList.add('scrolling');
    }

    // Clear existing timer
    if (this.scrollEndTimer) {
      clearTimeout(this.scrollEndTimer);
    }

    // Set new timer for scroll end detection
    this.scrollEndTimer = setTimeout(() => {
      this.isScrolling = false;
      element.classList.remove('scrolling');
      this.scrollEnd.emit();
    }, 150);

    // Check if scrolled to bottom
    const { scrollTop, scrollHeight, clientHeight } = element;
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      this.scrollEnd.emit();
    }
  }

  /**
   * Scroll to specific item index
   */
  scrollToIndex(index: number, behavior: ScrollBehavior = 'smooth'): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const element = this.elementRef.nativeElement;
    const targetScrollTop = index * this.itemHeight;

    element.scrollTo({
      top: targetScrollTop,
      behavior
    });
  }

  /**
   * Scroll to top
   */
  scrollToTop(behavior: ScrollBehavior = 'smooth'): void {
    this.scrollToIndex(0, behavior);
  }

  /**
   * Scroll to bottom
   */
  scrollToBottom(behavior: ScrollBehavior = 'smooth'): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const element = this.elementRef.nativeElement;
    element.scrollTo({
      top: element.scrollHeight,
      behavior
    });
  }

  /**
   * Get current viewport information
   */
  getCurrentViewport(): VirtualScrollViewport {
    return this.viewport();
  }

  /**
   * Check if item is currently visible
   */
  isItemVisible(index: number): boolean {
    const viewport = this.viewport();
    return index >= viewport.startIndex && index <= viewport.endIndex;
  }

  /**
   * Update item height dynamically
   */
  updateItemHeight(newHeight: number): void {
    this.itemHeight = newHeight;
    // Force viewport recalculation
    this.handleScroll();
  }

  /**
   * Refresh virtual scroll (useful after data changes)
   */
  refresh(): void {
    if (!isPlatformBrowser(this.platformId)) return;
    
    // Force recalculation
    this.handleScroll();
  }

  /**
   * Get visible items based on current viewport
   */
  getVisibleItems(): VirtualScrollItem[] {
    const viewport = this.viewport();
    return this.items.slice(viewport.startIndex, viewport.endIndex + 1);
  }

  /**
   * Estimate total content height
   */
  getTotalHeight(): number {
    return this.viewport().totalHeight;
  }

  /**
   * Get scroll percentage (0-100)
   */
  getScrollPercentage(): number {
    if (!isPlatformBrowser(this.platformId)) return 0;

    const element = this.elementRef.nativeElement;
    const { scrollTop, scrollHeight, clientHeight } = element;
    
    if (scrollHeight <= clientHeight) return 100;
    
    return Math.round((scrollTop / (scrollHeight - clientHeight)) * 100);
  }

  private cleanup(): void {
    if (this.scrollListener) {
      this.elementRef.nativeElement.removeEventListener('scroll', this.scrollListener);
    }

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    if (this.scrollEndTimer) {
      clearTimeout(this.scrollEndTimer);
    }
  }
}
