<div class="carousel">
  <!-- Лента -->
  <div class="carousel-track-wrapper">
    <div
      class="carousel-track"
      [style.transform]="'translateX(-' + currentIndex() * (100 / itemsPerView) + '%)'"
    >
      <ng-container *ngFor="let item of items">
        <div class="carousel-item" [style.flex]="'0 0 ' + (100 / itemsPerView) + '%'">
          <ng-container
            *ngTemplateOutlet="itemTemplate; context: { $implicit: item }"
          ></ng-container>
        </div>
      </ng-container>
    </div>
  </div>

  <!-- Кнопки -->
  <button class="nav left" (click)="prev()" [disabled]="currentIndex() === 0">
    <img src="../../../assets/images/main-v2/arrow-left.svg" alt="left arrow">
  </button>
  <button
  class="nav right"
  (click)="next()"
  [disabled]="currentIndex() === totalPages() - 1"
  >
    <img src="../../../assets/images/main-v2/arrow-right.svg" alt="right arrow">
  </button>

  <!-- Индикаторы -->
  <div class="indicators">
    <span
      *ngFor="let i of [].constructor(totalPages()); let idx = index"
      class="dot"
      [class.active]="idx === currentIndex()"
      (click)="goTo(idx)"
    ></span>
  </div>
</div>
