.carousel {
  position: relative;
  overflow: hidden;
  width: 100%;

  .carousel-track-wrapper {
    overflow: hidden;
  }

  .carousel-track {
    display: flex;
    transition: transform 0.4s ease-in-out;
    width: 100%;
  }

  .carousel-item {
    // flex: 0 0 calc(100% / var(--items-per-view, 3));
    box-sizing: border-box;
    padding: 0 15px;
  }

  .nav {
    position: absolute;
    top: 40%;
    transform: translateY(-50%);
    background: #fff;
    border: none;
    cursor: pointer;
    font-size: 2rem;
    z-index: 2;

    &.left {
      width: 72px;
      height: 72px;
      left: 0.5rem;
      border: 1px solid;
      border-radius: 50%;
      background: #FFF6E080;
      backdrop-filter: blur(10px);
      border: 1px solid #F1B94F;
      display: flex;
      align-items: center;
      justify-content: center;
      // background: url(assets/images/main-v2/direction-left.webp);
    }
    &.right {
      width: 72px;
      height: 72px;
      border-radius: 50%;
      // background: url(assets/images/main-v2/direction-right.webp);
      border: 1px solid;
      background: #FFF6E080;
      backdrop-filter: blur(10px);
      border: 1px solid #F1B94F;
      right: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }
  }

  .indicators {
    text-align: center;
    margin-top: 56px;

    .dot {
      display: inline-block;
      width: 32px;
      height: 4px;
      background: #FFE6AE;
      border-radius: 2px;
      margin: 0 8px;

      cursor: pointer;

      &.active {
        background: #99601A;
      }
    }
  }
}
