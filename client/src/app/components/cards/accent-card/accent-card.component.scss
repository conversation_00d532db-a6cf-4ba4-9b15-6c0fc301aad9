@import '../../../../assets/styles/new-palette';
@import '../../../../assets/styles/new-typography';

.accent-card {
    display: flex;
    flex-direction: column;
    padding: 1px;
    background-color: main(50);

    // padding: 22px 0;
    .card-bg {
        position: relative;
        overflow: hidden;
        height: fit-content;
        width: 100%;
        .background-mask-img {
            z-index: 2;
            position: sticky;
        }
        .card-img {
            position: absolute;
            border-radius: 6px;
            height: auto;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            top: 0;
            z-index: 1;
        }
    }

    .accent-card-title-section {
        padding: 0 22px 22px;
        background-color: main(100);
        border-radius: 0 0 8px 8px;
        margin-bottom: 1rem;
        .card-date {
            color: main(600);
            margin: 8px 0;
            @include caption-3;
            line-height: 100%;
            letter-spacing: 0;
            vertical-align: middle;
            text-align: end;
        }
        .card-level {
            color: main(600);
            margin: 8px 0;
            @include caption-3;
            line-height: 100%;
            letter-spacing: 0;
            vertical-align: middle;
        }
    
        .card-title {
            @include body-2;
            color: main(700);
            margin-top: 1rem;
        }
    }

    .card-description {
        color: main(500);
        @include body-1;
        padding: 0 22px;
    }
}