@import '../../../../assets/styles/new-palette';
@import '../../../../assets/styles/new-typography';

.join-card {
    display: flex;
    flex-direction: column;
    padding: 1px;
    background-color: main(50);
    width: 210px;
    max-width: 100%;
    transition: all 0.2s ease-in-out;
    padding: 1px;
    &:hover {
        max-width: 280px;
        width: 280px;
        .card-bg {
            .background-mask {
                width: 280px;
                background: url(../../../../assets/images/main-v2/join-card-mask-focus.webp) no-repeat center;
            }
        }
    }
    // padding: 22px 0;
    .card-bg {
        position: relative;
        overflow: hidden;
        height: fit-content;
        width: 100%;
        .background-mask {
            z-index: 2;
            position: sticky;
            transition: all 0.2s ease-in-out;
            background: url(../../../../assets/images/main-v2/join-card-mask.webp) no-repeat center;
            width: 210px;
            height: 159px;
            margin-bottom: -157px;
        }
        .card-img {
            border-radius: 110px 110px 6px 6px;
            height: auto;
            width: 100%;
            height: 100%;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
            z-index: 1;
            height: 510px;
        }
    }

    .card-date {
        color: main(700);
        margin: 32px 0 0;
        @include caption-2;
        line-height: 100%;
        letter-spacing: 0;
        vertical-align: middle;
    }

    .card-title {
        @include subtitle-1;
        color: main(600);
        margin-top: 24px;
    }
    .card-description {
        color: main(700);
        @include body-1;
        margin-top: 24px;
    }
}