import { CommonModule } from '@angular/common'
import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router'
import { TranslocoModule } from '@jsverse/transloco'
import { filter } from 'rxjs/operators'

export interface IBreadcrumb {
    label: string;
    url: string;
  }

  
@Component({
  selector: 'breadcrumb',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    TranslocoModule
  ],
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss']
})
export class BreadcrumbComponent implements OnInit {
  public breadcrumbs: IBreadcrumb[] = [];

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit() {
    this.breadcrumbs = this.buildBreadcrumb(this.activatedRoute.root);
  
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      const root = this.activatedRoute.root;
      this.breadcrumbs = this.buildBreadcrumb(root);
    });
  }
  
  private buildBreadcrumb(route: ActivatedRoute, url: string = '', breadcrumbs: IBreadcrumb[] = []): IBreadcrumb[] {
    let resolvedData = route.snapshot.data['resolvedBreadcrumb'];
    let label = route.snapshot.data['breadcrumb'] || '';
    const path = route.snapshot.url.map(segment => segment.path).join('/');

    const newUrl = path ? `${url}/${path}` : url;

    if (breadcrumbs.length === 0) {
      breadcrumbs.push({ label: 'Главная', url: '/ru' });
    }

    if (resolvedData && typeof resolvedData === 'object' && 'categoryName' in resolvedData) {
      const forumData = resolvedData as any;

      const categoryUrl = `/ru/forum/${forumData.categoryId}`;
      if (!breadcrumbs.some(b => b.url === categoryUrl)) {
        breadcrumbs.push({ label: forumData.categoryName, url: categoryUrl });
      }

      if (!breadcrumbs.some(b => b.label === forumData.topicName)) {
        breadcrumbs.push({ label: forumData.topicName, url: newUrl });
      }
    } else {
      const finalLabel = typeof resolvedData === 'string' ? resolvedData : label;

      if (finalLabel && !breadcrumbs.some(b => b.label === finalLabel)) {
        breadcrumbs.push({ label: finalLabel, url: newUrl });
      }
    }

    if (route.firstChild) {
      return this.buildBreadcrumb(route.firstChild, newUrl, breadcrumbs);
    }

    return breadcrumbs;
  }
  

  navigateTo(url: string): void {
    this.router.navigateByUrl(url);
  }
  
}
