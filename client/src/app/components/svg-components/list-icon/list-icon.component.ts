import { Component, inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from "@angular/common";

@Component({
  selector: 'app-list-icon',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './list-icon.component.html',
  styleUrl: './list-icon.component.scss'
})
export class ListIconComponent {
  platformId = inject(PLATFORM_ID);

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
    }
  }
}
