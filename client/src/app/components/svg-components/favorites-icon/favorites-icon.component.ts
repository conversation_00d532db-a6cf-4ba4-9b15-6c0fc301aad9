import { Component, inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from "@angular/common";

@Component({
  selector: 'app-favorites-icon',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './favorites-icon.component.html',
  styleUrl: './favorites-icon.component.scss'
})
export class FavoritesIconComponent {
  platformId = inject(PLATFORM_ID);

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
    }
  }
}
