@if (isVisible()) {
  <div class="modal-overlay" (click)="onClose()">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h3>Получить доступ к книге</h3>
        <button class="close-button" (click)="onClose()">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <div class="book-info">
          <img [src]="bookData().image" [alt]="bookData().title" class="book-cover">
          <div class="book-details">
            <h4>{{bookData().title}}</h4>
            <p class="book-author">{{bookData().author}}</p>
          </div>
        </div>

        <div class="price-info">
          @if(paymentType() === 'stripe') {
            <div class="price">Цена книги: {{bookData().priceEur}} EUR</div>
          } @else {
            <div class="price">Цена книги: {{bookData().priceRub}} RUB</div>
          }
        </div>

        <div class="payment-options">
          <h5>Выберите способ оплаты:</h5>
          <div class="payment-option">
            <input
              type="radio"
              id="stripe"
              name="paymentType"
              value="stripe"
              [checked]="paymentType() === 'stripe'"
              (change)="onPaymentTypeChange('stripe')">
            <label for="stripe">Stripe (Европа)</label>
          </div>
          <div class="payment-option">
            <input
              type="radio"
              id="yookassa"
              name="paymentType"
              value="yookassa"
              [checked]="paymentType() === 'yookassa'"
              (change)="onPaymentTypeChange('yookassa')">
            <label for="yookassa">ЮКасса (СНГ)</label>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="cancel-button" (click)="onClose()" [disabled]="isLoading()">
          Отмена
        </button>
        <button class="purchase-button" (click)="onPurchase()" [disabled]="isLoading()">
          @if (isLoading()) {
            <span class="loading-spinner"></span>
            Обработка...
          } @else {
            Купить книгу
          }
        </button>
      </div>
    </div>
  </div>
}
