import { Component, ElementRef, ViewChild, inject, PLATFORM_ID, On<PERSON>nit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef, ViewEncapsulation } from '@angular/core';
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Subject, takeUntil } from 'rxjs';
import { AdDisplayService, Advertisement } from '../../services/ad-display.service';
import { environment } from '@/env/environment';

@Component({
  selector: 'app-ad-dialog',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,  // Отключает изоляцию стилей
  templateUrl: './ad-dialog.component.html',
  styleUrls: ['./ad-dialog.component.scss']
})
export class AdDialogComponent implements OnInit, OnDestroy {
  @ViewChild('adDialog') adDialog!: ElementRef<HTMLDialogElement>;
  
  private platformId = inject(PLATFORM_ID);
  private sanitizer = inject(DomSanitizer);
  private adDisplayService = inject(AdDisplayService);
  private cdr = inject(ChangeDetectorRef);
  private destroy$ = new Subject<void>();
  
  environment = environment;
  currentAd: Advertisement | null = null;
  safeAdLink: SafeResourceUrl | null = null;
  isClosing = false;
  showContent = false;

  ngOnInit(): void {
    console.log('AdDialogComponent ngOnInit - Platform:', isPlatformBrowser(this.platformId));

    if (!isPlatformBrowser(this.platformId)) {
      console.log('AdDialogComponent: Not in browser, skipping initialization');
      return;
    }

    // Listen for ads to display
    this.adDisplayService.showAd$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (ad) => {
          console.log('AdDialogComponent received ad:', ad);
          console.log('AdDialogComponent state - isClosing:', this.isClosing, 'showContent:', this.showContent);
          if (ad && !this.isClosing && !this.showContent) {
            this.showAd(ad);
          } else {
            console.log('AdDialogComponent: Skipping ad display due to state conditions');
          }
        },
        error: (error) => {
          console.error('AdDialogComponent: Error in showAd$ subscription:', error);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Show the advertisement dialog
   */
  private showAd(ad: Advertisement): void {
    console.log('AdDialogComponent showAd called with:', ad);
    this.currentAd = ad;
    this.isClosing = false;
    this.showContent = true;

    // Prepare safe link for display
    this.safeAdLink = this.sanitizer.bypassSecurityTrustResourceUrl(ad.link);

    // Trigger change detection to update the template
    this.cdr.detectChanges();

    console.log('AdDialogComponent currentAd set to:', this.currentAd);

    // Use setTimeout to ensure the ViewChild is properly initialized and DOM is updated
    setTimeout(() => {
      // Show the dialog
      if (this.adDialog && isPlatformBrowser(this.platformId)) {
        console.log('AdDialogComponent showing modal dialog');
        const dialog = this.adDialog.nativeElement;

        try {
          // Remove hidden class first
          dialog.classList.remove('ad-dialog-hidden');

          // Check if dialog element supports showModal
          if (typeof dialog.showModal === 'function') {
            dialog.showModal();
            console.log('AdDialogComponent: Dialog showModal() called successfully');
          } else {
            // Fallback for browsers without dialog support
            console.warn('AdDialogComponent: Dialog element does not support showModal, using fallback');
            dialog.classList.add('ad-dialog-fallback');
            dialog.style.display = 'flex';
          }

          // Add opening animation class
          dialog.classList.add('ad-dialog-opening');

          // Remove opening class after animation
          setTimeout(() => {
            dialog.classList.remove('ad-dialog-opening');
            // ViewEncapsulation.None решает проблему отображения автоматически
          }, 300);
        } catch (error) {
          console.error('AdDialogComponent: Error showing dialog:', error);
          // Fallback: just show the dialog without modal behavior
          dialog.classList.remove('ad-dialog-hidden');
          dialog.style.display = 'flex';
        }
      } else {
        console.warn('AdDialogComponent: adDialog ViewChild not available or not in browser');
        console.log('AdDialogComponent debug - adDialog:', !!this.adDialog, 'isPlatformBrowser:', isPlatformBrowser(this.platformId));
      }
    }, 0);
  }

  /**
   * Close the ad dialog
   */
  closeAdDialog(): void {
    if (this.isClosing || !this.adDialog || !this.showContent) {
      return;
    }

    console.log('AdDialogComponent: Starting close animation');
    this.isClosing = true;
    const dialog = this.adDialog.nativeElement;

    console.log('AdDialogComponent: showContent before close:', this.showContent);

    // Add closing animation class
    dialog.classList.add('ad-dialog-closing');

    // Wait for animation to complete before actually closing
    setTimeout(() => {
      console.log('AdDialogComponent: Closing modal after animation');

      try {
        // Close the dialog if it supports the close method
        if (typeof dialog.close === 'function') {
          dialog.close();
        } else {
          // Fallback for browsers without dialog support
          dialog.style.display = 'none';
        }
      } catch (error) {
        console.error('AdDialogComponent: Error closing dialog:', error);
        dialog.style.display = 'none';
      }

      dialog.classList.remove('ad-dialog-closing');
      dialog.classList.remove('ad-dialog-fallback');
      dialog.classList.add('ad-dialog-hidden');

      // Clear data and flags after dialog is closed
      this.currentAd = null;
      this.safeAdLink = null;
      this.isClosing = false;
      this.showContent = false;

      // Notify the service that modal is closed
      console.log('AdDialogComponent: Notifying service that modal is closed');
      this.adDisplayService.notifyModalClosed();

      // Trigger change detection to update the template
      this.cdr.detectChanges();
    }, 250); // Animation duration
  }

  /**
   * Navigate to the ad link and close dialog
   */
  navigateToAdLink(): void {
    if (isPlatformBrowser(this.platformId) && this.currentAd) {
      window.open(this.currentAd.link, '_blank');
      this.closeAdDialog();
    }
  }

  /**
   * Handle clicking on the backdrop (outside the modal content)
   */
  onBackdropClick(event: MouseEvent): void {
    // Only close if clicking directly on the dialog element (backdrop) and content is visible
    if (event.target === this.adDialog?.nativeElement && this.showContent && !this.isClosing) {
      this.closeAdDialog();
    }
  }
}
