import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading-indicator',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="loading-indicator" [class.fullscreen-loading]="fullscreen()" [class.small]="size() === 'small'">
      <div class="loading-spinner" [class.small-spinner]="size() === 'small'"></div>
      @if (text()) {
        <p>{{ text() }}</p>
      }
    </div>
  `,
  styleUrls: ['./loading-indicator.component.scss']
})
export class LoadingIndicatorComponent {
  text = input<string>('');
  size = input<'normal' | 'small'>('normal');
  fullscreen = input<boolean>(false);
}
