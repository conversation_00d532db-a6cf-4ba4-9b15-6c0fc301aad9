.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  p {
    font-family: Prata;
    font-size: 18px;
    color: var(--font-color1);
    margin-top: 20px;
  }

  &.fullscreen-loading {
    min-height: 300px;
  }

  &.small {
    padding: 20px;
    
    p {
      font-size: 14px;
      margin-top: 10px;
    }
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--text-color);
  border-top: 3px solid var(--book_about);
  border-radius: 50%;
  animation: spin 1s linear infinite;

  &.small-spinner {
    width: 20px;
    height: 20px;
    border-width: 2px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
