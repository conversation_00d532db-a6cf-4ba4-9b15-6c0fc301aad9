@media (max-width: 1550px) {
    .burger {
        margin-right: 25px;
        width: 29px;
        height: 16px;
        background-size: contain;
    }

    .navbar a {
        font-size: 17px;
        line-height: 20px;
    }

    .navbar .button_img_before a {
        font-size: 15px;
        line-height: 15px;
    }

    .navbar li.item.button_img_before::before {
        width: 160px;
        height: 33px;
    }

    .navbar li.button_img_before button {
        margin-top: 2px;
    }

    .navbar li.button_img_before {
        width: 160px;
        height: 33px;
        margin: 0 20px 0 0;
    }

    .user_logo_:not(.head_section) {
        width: 35px;
        height: 35px;
    }

    .navbar li {
        margin-right: 20px;
    }

    .top_block {
        height: 900px;
    }

    .img_mnb {
        max-height: 75vh;
    }

    .quick-links .link-icons.tr_ {
        max-width: 1170px;
    }

    .frame_w {
        left: 10px;

        &.mirr {
            right: 10px;
        }
    }

    .frame_w:not(.dec) {
        width: 57px;
    }

    .m_btn.mid {
        margin-bottom: 80px;
        margin-top: 45px;
        height: 61px;
        width: 100%;

        span {
            font-size: 22px;
        }
    }

    .m_btn.wide {
        width: 100%;
        height: 77px;
    }

    .m_btn.wide span {
        font-size: 22px;
    }

    .secnd_block-wrap {
        padding: 80px 0;
    }

    .def-block_block-wrap {
        padding: 80px 0;
    }

    .m_btn.wide {
        margin-bottom: 55px;
    }

    .frame_ {
        width: 280px !important;
        height: 280px;
    }

    .carousel-arrow {
        width: 44px;
        height: 22px;
    }

    .carousel-arrow.right-arrow {
        right: 60px;
    }

    .carousel-arrow.left-arrow {
        left: 60px;
    }

    .carousel-container {
        max-width: 1050px;

        &.middle {
            max-width: 900px;
        }

        .carousel-item {
            .carousel-content {
                bottom: 80px;
                width: 280px;

                .carousel-title {
                    max-width: 80%;
                    font-size: 22px;
                    line-height: 22px;
                    margin-bottom: 7px;
                }

                .carousel-date img {
                    height: 41px;
                }
            }
        }
    }

    .star_ {
        width: 46px;
        height: 46px;
        top: -37px;
    }

    .quick-links .link-icons {
        max-width: 900px;

        &.complex {
            max-width: 1170px;
        }

        .link-item {
            max-width: 120px;

            .image_w {
                img.sign {
                    scale: 0.4;
                }

                .round {
                    width: 120px;
                    height: 120px;
                }
            }

            span {
                font-size: 18px;
                line-height: 20px;
            }
        }
    }
}

@media (max-width: 1350px) {
    .cl_bun {

        span {
            display: block;
            opacity: 0;
            width: 30px;
        }
    }

    .cl_bun:after {
        right: 2px;
    }

    .playControls__elements {
        padding: 17px 34px 19px 34px;
    }

    .card-wrap,
    .m_tab {
        margin-left: 15px;
        margin-right: 15px;
    }

    .top_block {
        height: 850px;
    }

    .menu_wrap {
        padding: 0 50px;
    }
}

@media (max-width: 1239px) {
    .top_block {
        height: 800px;
    }

    .navbar li.button_img_before {
        width: 150px;
        height: 29px;
    }

    .navbar .button_img_before a {
        font-size: 13px;
    }

    .user_logo_:not(.head_section) {
        width: 40px;
        height: 40px;
    }

    .user-dropdown:not(.head_section, .app_section) {
        font-size: 15px;
    }

    .navbar li.item.button_img_before::before {
        width: 150px;
        height: 29px;
    }

    .burger {
        margin-right: 14px;
    }

    .navbar a {
        font-size: 15px;
    }

    .navbar li {
        margin-right: 15px;
    }

    .l-container.l-fullwidth {
        width: 100%;
    }

    .carousel-container {
        max-width: 930px;
    }

    .frame_s {
        width: 160px;
    }

    .ic_rud:not(.wide__) {
        width: 65px;
        margin-bottom: -25px;
    }

    .quick-links .link-icons.complex {
        max-width: 895px;
    }

    .frame_s.frame_bottom {
        margin-bottom: -26px;
    }

    .quick-links .link-icons.complex:not(.bottom_ln) .link-item {
        width: 133px;
    }

    .bottom_ln {
        .frame_s {
            width: 210px;
        }

        .frame_s.frame_top {
            margin-top: -35px;
        }
    }

    .frame_s.frame_top {
        margin-top: -25px;
        margin-bottom: -20px;
    }
}

@media (max-width: 1200px) {
    .top_block {
        height: 750px;
    }

    .close_btn {
        top: 10px;
    }

    .user-dropdown:not(.head_section, .app_section) {
        font-size: 13px;
        line-height: 16.5px;
        padding: 0 0 0 10px;
    }

    .top_block {
        margin-top: -75px;
    }

    .carousel-arrow.left-arrow {
        left: 12px;
    }

    .carousel-arrow.right-arrow {
        right: 12px;
    }
}

@media (max-width: 1100px) {
    .pl_center_wrap {
        width: 43.67%;
    }

    .playControls__elements {
        justify-content: space-evenly;
    }

    .f_par {
        font-size: 16px !important;
        line-height: 24px !important;
    }

    .footer_wrap {
        .m_grid.nav_wrap {
            justify-content: unset;
        }

        .f-tr_block.sd_ {
            margin-left: 20px;
        }
    }

    .f_head {
        font-size: 40px !important;
        line-height: 44px !important;
    }

    .soc_block {
        position: absolute;
        bottom: -64px;
        right: 0;
    }

    .abs_sm {
        position: absolute;
        bottom: -65px;
        left: 0;
    }

    .bord_bt {
        border-bottom: none !important;
        margin-top: auto;
        justify-content: space-between;
    }

    .f-tr_block {
        width: 43% !important;
    }

    .f-tr_block.sd_ {
        width: 38% !important;
    }

    .flex_item {
        margin-right: 38px;
    }

    .footer_wrap {
        min-height: 525px;
        padding: 0 20px 35px 20px !important;

        .nav_wrap {
            border-bottom: 1px solid var(--menu_font);
            padding-bottom: 50px;
        }
    }

    .top_block {
        height: 700px;
    }

    .tags {
        font-size: 16px;
    }

    .block_quote {
        padding: 24px;
        margin: 35px 0;
    }

    .quotes_ {
        width: 51px;
        height: 51px;
    }

    .img_description {
        font-size: 20px;
        line-height: 26px;
    }

    .html_wrap {
        figcaption {
            font-size: 20px;
            line-height: 26px;
            margin-top: 15px;
        }
    }

    .carn_b {
        display: none;
    }

    h1.dec_head-title {
        font-size: 24px !important;
        font-weight: 400 !important;
        line-height: 24px;
        margin: 50px 0;
    }

    .wrapper_line {
        padding: 90px 0 100px 0;
    }

    .tr_cont {
        width: 180px;
        margin-bottom: 0;
    }

    .button_sm_wrap span {
        font-size: 11.52px;
        line-height: 11.52px;
    }

    .three_items .tablet_mob {
        display: block;
        width: 182px;
        height: 162px;
        background: url(assets/images/frame_md.webp);
        background-repeat: no-repeat;
        background-size: contain;
    }

    .button_sm_wrap {
        width: 152px;
        height: 36px;
    }

    .lbl_c {
        font-size: 18px;
        line-height: 18px;
        max-width: 70%;
        margin: 12px auto 5px auto;
    }

    .lbl_t {
        font-size: 10px;
        line-height: 10px;
        margin: 0 auto 2px auto;
    }

    .frame_t.desctop {
        display: none;
    }

    .quick-links .link-icons.tr_ {
        height: 200px;
        margin-top: 50px;
        max-width: 870px;
    }

    .close_btn {
        right: 115px;
    }

    .m_btn.light_ {
        width: 180px;
        height: 89px;
    }

    .m_btn {
        width: 285px;
        height: 82px;
    }

    .m_btn span {
        font-size: 18px;
    }

    .m_btn h1 {
        font-size: 18px;
    }

    .top_block {
        margin-top: -65px;
    }

    .user-menu.auth_ {
        margin-left: 27px;
    }

    .user_logo_:not(.head_section) {
        width: 35px;
        height: 35px;
    }

    .button_img_before,
    .item.serch {
        display: none !important;
    }
}

@media (max-width: 1079px) {
    .playControls__inner {
        height: 70px;
    }

    .playControl.playControls__control {
        svg {
            height: 35px;
        }
    }

    .playControls__control.playControl {
        width: 35px;
        height: 35px;

        svg {
            max-width: 100%;
        }
    }

    .playControls__control {
        width: 16px;
        height: 14px;
    }

    .nav_contner {
        height: 35px;
        margin-bottom: 0;
    }

    .playControls__control.playControls__shuffle {
        width: 15.5px;
        height: 12.5px;
        display: flex;

        button:before {
            background-size: contain;
        }

        button:after {
            background-size: contain;
        }
    }

    .playControls__control,
    .playControls__control:not(:first-child) {
        margin-left: 12px;

        &.playControl {
            margin-left: 12px;
            margin-right: 0;
        }
    }

    .playbackTimeline__progressHandle {
        height: 8px;
        width: 3px;
        margin-top: -3px;
        margin-left: -4px;
    }

    .playbackTimeline__progressBackground {
        height: 2px;
    }

    .playbackTimeline__progressBar {
        height: 2px;
    }

    .playbackTimeline__progressHandle::before {
        top: 2px;
    }

    .playbackTimeline__progressHandle::after {
        top: 2px;
    }

    .playbackTimeline__duration,
    .playbackTimeline__timePassed {
        font-size: 11px;
    }

    .playControls__control.playControls__repeat {
        width: 14.5px;
        height: 13.5px;
        display: flex;

        button {
            background-size: contain;
        }
    }

    .list__button {
        width: 15px;
        background-size: 15px 15px;
    }

    .cl_bun:after {
        width: 14px;
        height: 14px;
        background-size: contain;
    }

    .volume__iconWrapper button {
        margin-right: 12px;
    }

    .cl_bun {
        line-height: 14px;
        width: 20px;
    }

    .cl_bun span {
        width: unset;
    }

    .volume__iconWrapper {
        svg {
            width: 15px;
            height: 15px;
        }
    }

    .volume__button.speed {
        font-size: 13px;
    }

    .pl_title {
        font-size: 14px;

        &.auth_sm {
            font-size: 12px;
        }
    }

    .playbackTimeline__progressWrapper {
        padding: 6px 0;
        margin: 0 5px 0;
    }

    .skipControl__previous,
    .playControls__next {
        background-size: contain;
    }

    .html_wrap {
        font-size: 18px;
        line-height: 26px;

        a.mention {
            font-size: 18px;
            line-height: 26px;
        }
    }

    .carousel-container {
        max-width: 690px;
    }
}

@media (max-width: 950px) {
    .soc_block {
        bottom: unset;
        right: unset;
        top: 0;
    }

    .user-menu.auth_ {
        margin-left: 5px;
    }

    .f-tr_block.sd_ {
        margin-top: 70px;
    }

    .footer_wrap .nav_wrap {
        padding-bottom: 15px;
    }

    .top_block {
        height: 650px;
    }

    .secnd_block-wrap {
        padding: 54px 0;
    }

    .def-block_block-wrap {
        padding: 54px 0;
    }

    .m_btn.wide {
        max-width: unset;
    }

    .m_btn.wide {
        margin-bottom: 15px;
        overflow: hidden;
    }

    .m_btn.mid {
        margin-bottom: 50px;
        margin-top: 20px;
    }

    .quick-links .link-icons .link-item.decoration {
        display: none;
    }

    .navbar li {
        margin-right: 5px;
    }

    .row_btns {
        flex-wrap: wrap;
        justify-content: space-evenly;
    }

    .sidebar_cl {
        left: -85%;
        width: 85%;
    }

    .top_block {
        margin-top: -45px;
    }

    .user-dropdown:not(.head_section, .app_section) {
        font-size: 11px;
    }

    .notifications img {
        width: 28px;
        height: 28px;
    }

    .menu_wrap {
        padding: 0 10px;
    }

    .quick-links .link-icons.complex {
        padding: 0 20px;
    }
}

@media (max-width: 850px) {
    .playControls__elements {
        padding: 17px 15px 19px 15px;
    }

    .f-tr_block.sd_ {
        width: 50% !important;
    }

    .side_head {
        padding: 0 41px 0 35px;
    }

    .list_menu {
        padding: 39px 10px 10px 35px;
    }

    .side_ {
        padding: 10px 10px 52px 35px;
    }

    .social_side_ {
        padding-left: 35px;
    }

    .quick-links .link-icons.complex.bottom_ln {
        justify-content: center;
    }

    .quick-links .link-icons.bottom_ln .link-item {
        margin: 0 18px 60px 18px;
    }

    .bottom_ln .frame_bottom {
        position: absolute;
        display: block;
        width: 160px;
        bottom: 0;
    }

    .quick-links .link-icons .link-item.visible_item {
        display: flex;
    }

    .quick-links .link-icons.complex.bottom_ln .ic_rud {
        display: none;
    }

    .bottom_ln .frame_s.frame_top {
        width: 160px;
    }

    .quick-links .link-icons.complex.bottom_ln .link-item {
        width: 133px;
        background-size: 90%;
    }

    .bottom_ln .frame_s.frame_top {
        margin-top: -25px;
        margin-bottom: -25px;
    }

    .quick-links .link-icons.complex.bottom_ln .link-item.col {
        min-height: 85px;
    }

    .quick-links .link-icons.complex .link-item span.t_cont {
        display: none;
    }

    .quick-links .link-icons.complex.bottom_ln .link-item.col span.txt_ {
        margin: -5px auto 0 auto;
        ;
    }

    .hide_msd {
        display: none !important;
    }

    .quick-links .link-icons.tr_ {
        padding: 0 15px;
    }

    .show_msd {
        display: flex;
    }

    .carousel-container {
        max-width: 615px;
    }

    .col_md {
        flex-direction: column;
    }

    .quick-links .link-icons.complex .link-item {
        background-size: cover;
    }

    .quick-links .link-icons.complex .link-item span {
        max-width: 85%;
    }

    .quick-links .link-icons.complex .link-item span.txt_ {
        margin-bottom: -30px;
    }

    .full_sm {
        width: 100%;
    }

    .mbtm {
        margin-bottom: 80px;
    }

    .col_min_md {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .lan_wrap .lan_switch {
        right: -15px;
    }

    .volume__sliderWrapper.list {
        left: -453px;
        bottom: 60px;
        width: 510px;
    }

    .que_lst {
        font-size: 16px;
        left: 20px;
    }

    .playControls__elements {
        padding: 10px 2px 10px 2px;
    }

    .dropdown-header {
        padding: 0 15px 0 14px !important;
        border-radius: 10px !important;
        height: 40px !important;
    }

    .dropdown_c {
        font-size: 18px !important;
        line-height: 24px !important;
    }

    .srchh {
        height: 16px !important;
        width: 16px !important;
        bottom: 10px !important;
    }

    .dropdown-item:not(.cat_i) {
        min-height: 40px !important;
    }

    input.item_ {
        width: 16px !important;
        height: 16px !important;
    }

    p.item_ {
        width: 16px !important;
        height: 16px !important;
    }

    .search-input {
        height: 34px !important;
        font-size: 18px !important;
        line-height: 18px !important;

        &::placeholder {
            font-size: 18px !important;
            line-height: 18px !important;
        }
    }

    .format-options input[type=checkbox]:checked+span::after {
        left: 10px;
        top: 4px;
        width: 9px;
        height: 14px;
    }

    .format-options label {
        font-size: 18px;
    }

    .format-options span {
        width: 28px;
        height: 28px;
    }

    dialog.stylized_wide {
        width: 90% !important;
        padding: 40px 12px !important;

        &.h_auto {
            height: fit-content !important;
        }
    }

    .center_logo {
        display: flex;
    }

    .f-tr_block {
        width: 45% !important;
    }

    .f-tr_block.sd_ {
        width: 51% !important;
    }

    .checked {
        background-size: 16px 16px !important;
    }

    .selectd_ {
        p.item_ {
            background-size: 16px 16px !important;
        }
    }

    .footer_wrap .nav_wrap {
        padding-bottom: 60px;
    }

    .side_head {
        background: var(--main-back-stripes_md), var(--main-back-gradient);
        background-size: 768px;
        background-position: center;
        background-repeat: repeat-x;
        height: 67px;
        padding: 0 41px 0 34px;

        .center_logo {
            top: 4px;
        }

        .user_logo_.head_section {
            margin-left: 0;
        }

        .user-dropdown,
        .user-dropdown.has-arrow {
            display: flex;
        }

        .close_butn {
            width: 27px;
            height: 27px;
            margin-left: 26px;
        }

        .them_cn {
            width: 41px;
            height: 41px;
        }
    }


    .lan_btn {
        background: var(--circl_lang);
        width: 38px;
        height: 38px;

        p {
            margin-top: -1px;
        }
    }

    .theme_choose {
        margin-right: 20px;
    }

    .rh_block {
        width: fit-content;
    }

    .side_head::after {
        top: 67px;
    }

    .footer_wrap {
        padding: 56px 34px 130px 34px !important;
        height: fit-content !important;
    }

    .header_stunt {
        height: 67px;
    }

    .menu_wrap {
        background: var(--main-back-stripes_md), var(--main-back-gradient);
        background-size: 768px;
        background-position: center;
        background-repeat: repeat-x;
        height: 67px;
        padding: 0 34px;
    }

    .f-tr_block.sd_ {
        width: 47% !important;
    }

    .one_of {
        margin-bottom: -30px !important;
        margin-right: 0 !important;
    }

    .f-tr_block {
        width: 47%;
    }

    .flex_item {
        margin-right: 55px;
    }

    .f_text_ {
        line-height: 26px !important;
        margin-bottom: 16px !important;
    }

    .center_logo {
        top: 2px;
    }

    .f_par {
        margin-top: 22px;
    }

    .burger {
        margin-right: 16px;
        padding: 10px 0;
        background: var(--burger_md);
        width: 41px;
        height: 26px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
    }

    .user-menu.hide:not(.auth_) {
        display: none;
    }

    .user_logo_ {
        width: 40px;
        height: 40px;
        margin-left: 20px;
    }

    .user-dropdown,
    .user-dropdown.has-arrow {
        display: none;
    }

    .nav_cont_sch {
        cursor: pointer;
        background: var(--nav_cont_sch);
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        width: 35px;
        height: 35px;
        margin-left: auto;
    }

    .navbar ul li:not(.button_img) {
        display: none;
    }

    breadcrumb {
        margin: 44px 0 30px 24px;
    }

    .buttons_wrap {
        bottom: 24%;
    }

    .img_mnb {
        left: -90px;
    }

    .row_btns:not(.bottom_t) {
        display: none;
    }

    .m_btn.mid {
        height: 44px;
    }

    .m_btn.mid span {
        font-size: 15px;
    }

    .m_btn.wide {
        margin-top: 35px;
    }

    .m_btn.wide span {
        font-size: 15px;
    }

    .m_btn.wide {
        height: 47px;
    }

    .m_btn.mid {
        margin-bottom: 70px;
    }

    .quick-links .link-icons .link-item {
        width: 190px;
        max-width: unset;
        margin-bottom: 30px;
    }

    .quick-links .link-icons {
        flex-wrap: wrap;
        padding: 0 50px;
    }

    .def-block_block-wrap {
        padding: 85px 0;
    }

    .secnd_block-wrap {
        padding: 85px 0;

        .row_btns {
            display: flex;
            position: relative;
            z-index: 1;
            max-width: 90%;
            margin: 0 auto;
        }
    }

    .user-dropdown {
        padding: 5px 14px 5px 20px;
    }

    .sidebar_cl {
        left: -100%;
        width: 100%;
    }

    header {
        z-index: 110;
    }

    .side_blbc {
        display: none;
    }

    .user-menu.auth_ {
        margin-left: 0;
    }
}

@media (max-width: 700px) {
    .f_head {
        font-size: 30px !important;
        line-height: 30px !important;
    }

    .f_text_ {
        font-size: 17px !important;
        line-height: 20px !important;
    }

    .one_of {
        margin-bottom: -10px !important;
    }

    .und {
        font-size: 17px !important;
    }
}

@media (max-width: 650px) {
    .abs_sm {
        flex-direction: column;
        bottom: -85px;
    }

    dialog.stylized_wide .cont_mod {
        width: 100%;
    }
}

@media (max-width: 600px) {
    breadcrumb {
        margin: 33px 0 28px 10px;
    }

    .lan_switch {
        top: 15px;
    }

    .lan_wrap .lan_switch {
        right: -20px;
    }

    .lan_switch {
        right: -17px;
    }

    .item_menu {
        line-height: 28px;

        li {
            font-size: 18px;
        }
    }

    .list_menu {
        padding: 30px 10px 10px 30px;
    }

    .side_ {
        padding: 10px 10px 46px 30px;
    }

    .social_side_ {
        padding-left: 30px;
    }

    .side_head {
        padding: 0 30px;
    }

    .sidebar_cl .button_img a {
        width: 150px;
        height: 37px;
        font-size: 15px;
        line-height: 15px;
    }

    .sidebar_cl .button_img.reg a {
        margin-left: 15px;
    }

    .html_wrap {
        font-size: 15px;
        line-height: 22px;

        a.mention {
            font-size: 15px;
            line-height: 22px;
        }
    }

    .soc_block {
        top: unset;
        bottom: -190px;
        left: 0;
    }

    .f_par {
        margin-top: 0;
    }

    .footer_wrap {
        padding: 0px 34px 165px 34px !important;
        height: fit-content !important;
        padding: 55px 34px 240px 34px !important;
    }

    .footer_wrap .f-tr_block.sd_ {
        margin-left: 0;
    }

    .one_of_ {
        margin-right: 40px !important;
    }

    .footer_wrap .nav_wrap {
        flex-direction: column;
    }

    .f-tr_block.sd_ {
        width: 100% !important;
        margin-top: 36px;
    }

    .f-tr_block {
        width: 100% !important;
    }

    .abs_sm {
        bottom: -108px;
    }

    .else_bt {
        width: 155px;
        height: 41px;
        margin-bottom: 12px;
        font-size: 17px;
    }

    .flex_item {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .f_par {
        font-size: 15px;
        line-height: 22px;
    }

    .auth_p {
        font-size: 17px;
    }

    dialog.stylized_ {
        min-width: 306px;
        padding: 46px 0 50px 0;
    }

    button.else_bt.reg_bt {
        width: 210px;
        height: 45px;
    }

    .x_bt {
        right: 14px;
        top: 13px;
        width: 16px;
        height: 16px;
    }

    .img_mnb {
        display: none;
    }

    .three_items .frame_t {
        width: 150px;
        height: 133px;
    }

    .quick-links .link-icons.tr_ {
        height: 130px;
    }

    .button_sm_wrap span {
        font-size: 10px;
    }

    .quick-links .link-icons.tr_ {
        flex-wrap: wrap;

        .three_items {
            width: 170px;
            height: 150px;
        }
    }

    .button_sm_wrap {
        width: 130px;
        height: 28px;
    }

    .lbl_c {
        font-size: 14px;
        line-height: 14px;
        margin: 8px auto 4px auto;
        max-width: 55%;
    }

    .lbl_t {
        max-width: 55%;
    }

    .col_md {
        flex-direction: row;
        justify-content: space-around;
    }

    .col_min_md {
        flex-direction: column;

        .link-item {
            min-height: 80px;
        }
    }

    .quick-links .link-icons.complex .link-item span {
        max-width: 73%;
    }

    .quick-links .link-icons .link-item {
        margin-bottom: 55px;
    }

    .frame_s {
        width: 149px;
    }

    .quick-links .link-icons .link-item span {
        font-size: 15px;
        line-height: 15px;
    }

    .mbtm {
        margin-bottom: 0;
    }

    .col_min_md {
        width: fit-content;
    }

    .top_block {
        align-items: center;
    }

    .buttons_wrap {
        position: absolute;
    }

    .venzs_ {
        display: none;
    }

    .m_btn.light_ {
        width: 158px;
        height: 83px;
    }

    .m_btn span {
        font-size: 15px;
    }

    .m_btn h1 {
        font-size: 15px;
    }

    .menu_wrap {
        padding: 0 23px;
    }

    .nav_cont_sch {
        width: 30px;
        height: 30px;
    }

    .user_logo_ {
        width: 30px !important;
        height: 30px !important;
        margin-left: 14px;
    }

    .center_logo {
        top: 1px;
        width: 70px;
        height: 70px;
    }

    .menu_wrap {
        background-size: 585px;
        height: 50px;
    }

    .header_stunt {
        height: 50px;
    }

    .burger {
        width: 30px;
        height: 19px;
    }

    .buttons_wrap {
        bottom: 15%;
    }

    .quick-links .link-icons.complex {
        margin-top: 70px;
    }

    .side_head {
        background-size: 560px;
        height: 46px;
    }

    .side_head::after {
        top: 46px;
    }

    .side_head .close_butn {
        width: 23px;
        height: 23px;
        margin-left: 17px;
        background-size: contain;
    }

    .side_head .center_logo {
        top: 1px;
        width: 70px;
        height: 70px;
    }

    .side_head .them_cn {
        width: 30px;
        height: 30px;
        background-size: cover, 70%;
    }

    .theme_choose {
        margin-right: 14px;
    }

    .lan_btn p {
        font-size: 14px;
    }

    .lan_btn {
        width: 30px;
        height: 30px;
    }
}

@media (max-width: 570px) {
    dialog.stylized_wide {
        padding: 40px 20px 20px 20px !important;
    }

    .icons_w.is-liked {
        .like_w {
            width: 17px !important;
            height: 16px !important;
        }
    }

    .icon-wrap.like_w {
        svg {
            width: 17px;
            height: 16px;
        }
    }

    .icons_w.in-favourites .star_w {
        width: 18px;
        height: 17px;
    }

    .icon-wrap.star_w {
        svg {
            width: 18px;
            height: 17px;
        }
    }

    .main_art-image {
        margin: 30px 0 36px 0 !important;
        position: relative;
        height: unset !important;
        max-height: unset !important;
        aspect-ratio: 16 / 9;
        border-radius: 10px !important;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }



    .icons_w:hover.in-favourites .star_w {
        width: 18px;
        height: 17px;
    }

    .icons_w {
        margin-top: 0;
    }

    .icons_w:hover .content_w {
        height: 16px;
        width: 16px;
    }

    .icons_w:hover .share_w {
        width: 16px;
        height: 16px;
    }

    .icons_w .icon-wrap:not(.cal_w, .clock_w) {
        svg {
            width: 16px;
            height: 16px;
        }
    }
}

@media (max-width: 500px) {
    .hidden {
        position: relative;
        top: -100%;
    }

    .app-additional-mobile-menu {
        display: flex;
    }

    .pl_title {
        font-size: 20px;
        line-height: 1;
        color: var(--text-color23);

        &.auth_sm {
            font-size: 15px;
            line-height: 2;
            color: var(--text-color);
        }
    }

    .pl_center_wrap {
        width: 100%;
    }

    .playControls__control,
    .playControls__control:not(:first-child) {
        margin-left: 15px;
    }

    .shuffleControl:before {
        background-image: var(--no-shfl);
    }

    .shuffleControl:after {
        background-image: var(--shoffl);
    }

    .playControls__control.playControls__shuffle {
        width: 22px;
        height: 22px;
    }

    .playControls__control.playControls__repeat {
        width: 24px;
        height: 22px;

        button {
            background-repeat: no-repeat;
            background-position: center;
        }
    }

    .repeatControl {
        &.no-repeat {
            background: var(--no-repeat);
        }

        &.repeat-all {
            background-image: var(--all-repeat);
        }

        &.repeat-one {
            background-image: var(--one-repeat);
        }
    }

    .playbackTimeline__progressBackground {
        background-color: var(--pl_line1);
    }

    .playbackTimeline__progressBar {
        background-color: var(--line_back1);
    }

    .cl_bun {
        position: absolute;
        top: 20px;
        right: 45px;
    }

    .cl_bun:after {
        content: "";
        position: absolute;
        top: 0;
        right: -28px;
        display: block;
        width: 16px;
        height: 16px;
        background-repeat: no-repeat;
        background-position: 50%;
        background-image: var(--ard);
    }

    .playControls__control.playControl {
        width: 48px;
        height: 48px;
        margin-left: 0;

        svg {
            height: 48px;
        }
    }

    .skipControl__previous {
        background-image: var(--nav-pl);
        width: 15px;
        height: 15px;

        &.skipth {
            display: block;
        }
    }

    .skipControl__next {
        background-image: var(--nav-pl);
        width: 15px;
        height: 15px;
        margin-left: 20px !important;

        &.skipth {
            display: block;
            transform: none;
            margin-left: 15px !important;
        }
    }

    .mb_ver {
        display: block;
    }

    .dt_ver {
        display: none;
    }

    .cal_wrp {
        display: flex;
        line-height: 1;
        margin-top: 4px;
    }

    .playbackTimeline__progressBackground {
        height: 3px;
    }

    .playbackTimeline__progressHandle::after {
        display: none;
    }

    .playbackTimeline__progressHandle::before {
        display: none;
    }

    .playbackTimeline__progressHandle {
        height: 10px;
        width: 10px;
        margin-top: -3px;
        margin-left: -1px;
        border-radius: 50%;
        background-color: var(--line_handle1);
    }

    .playbackTimeline__duration,
    .playbackTimeline__timePassed {
        margin-top: 16px;
    }

    .playbackTimeline__progressWrapper {
        margin: 0 -16px;
    }

    .playbackTimeline__progressBar {
        height: 3px;
    }

    .playbackTimeline__duration,
    .playbackTimeline__timePassed {
        font-family: Satoshi;
        font-weight: 400;
        font-size: 11.53px;
        // letter-spacing: 3%;
        color: var(--menu_font1);
    }

    .cust_wd {
        width: 97%;
        padding: 0;
        margin-bottom: 15px;
    }

    .playControls {
        height: 100%;
    }

    .playControls__inner {
        &:not(.main-contetnt-wrapper) {
            height: calc(100% - 50px);
            margin-top: 50px;
            background: var(--main-background-image);
            background-size: 176px, contain;
            background-repeat: repeat, repeat;
        }
        &:has(.main-contetnt-wrapper) {
            background: #fff6e0;
        }
    }

    .playControls__inner::before {
        &:not(.main-contetnt-wrapper) {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 425px;
            background: var(--top-cover);
            background-size: cover;
            background-repeat: no-repeat;
            z-index: -1;

        }
    }

    .g-z-index-control-bar {
        z-index: 11;
    }

    .playControls__wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .playControls__elements {
        display: flex;
        position: unset;
        flex-direction: column;
        padding: 0 22px 25px 22px;
        justify-content: space-between;
        height: fit-content;
    }

    .spec_onee {
        height: fit-content;
    }

    .nav_contner {
        height: 48px;
        margin-top: 52px;
    }

    .list__button {
        height: 17px;
    }

    .volume__button.speed {
        color: var(--line_handle1);
        font-size: 20px;
        line-height: 21px;
    }

    .nav_playr:not(.mobile_v) {
        display: none;
    }

    .nav_playr.mobile_v {
        display: flex;
        justify-content: space-between;
        margin-left: 0;
        width: 100%;
        margin-top: calc(52px + 6%);

        .list__button {
            background: var(--listt_);
            width: 22px;
            height: 18px;
        }

        .volume__button.speed {
            background: var(--1speed);
            font-size: 0;
            width: 30px;
            height: 20px;
        }

        .mob_star {
            background: var(--l-star);
            width: 24px;
            height: 22px;
        }

        .mob_like {
            background: var(--l-heart);
            width: 24px;
            height: 22px;

            &.is-liked {
                background: var(--lk-heart);
            }
        }

        .mob_lst {
            background: var(--add-p);
            width: 22px;
            height: 22px;
        }

        .mob_share {
            background: var(--l-share);
            width: 22px;
            height: 22px;
        }
    }

    .volume__iconWrapper button {
        margin-right: 0;
    }

    .spinn_.show_list {
        position: fixed;
        top: 0;
        right: 0;
        display: flex;
        width: 100%;
        height: 50%;
        z-index: 5;
        background-color: var(--blur_main);
    }

    .volume__sliderWrapper.list {
        position: fixed;
        left: 0;
        bottom: 0;
        min-height: 92% !important;
        width: 100%;
    }

    .volume__sliderWrapper {
        left: -24px;
        bottom: 51px;
    }

    .playControls__bgg {
        margin: 30px 18px 18px 18px;
        position: unset;
        height: 410px;
        background: url(../../assets/images/avatar_.png);
        margin: 18px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }

    .playControls__bg {
        position: unset;
        height: 410px;
        background: var(--venz_);
        margin: 18px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }

    .album_cover {
        position: unset;
        height: 410px;
        background: var(--venz_cover);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }

    .cursor-move.list__ {
        display: none;
    }

    .play_list {
        padding-right: 5px;
        padding-left: 12px;
    }

    .que_lst {
        display: none;
    }

    .ul-list {
        margin-top: 60px;
    }

    .volume__sliderWrapper.list:before {
        opacity: 0;
    }

    .bs_wrapper_ {
        right: unset !important;
        top: 14px !important;
    }

    .close_list {
        top: 20px;
        right: 15px;
    }

    .pl_center_wrap {
        display: flex;
        flex-direction: column-reverse;
    }

    .hvr:hover .on_hover {
        right: 60px;
    }

    .btn_item_wrapper {
        margin-left: 10px !important;
    }

    .ent_choose_blc {
        display: flex;
        position: fixed;
        top: 0;
        width: 102%;
        height: 103vh;
        z-index: 112;
        background-color: var(--blur_main);
    }

    .theme_choose:hover {
        .lan_switch {
            opacity: 0;
            z-index: 0;
        }
    }

    .lan_wrap:hover {
        .lan_switch {
            opacity: 0;
            z-index: 0;
        }
    }

    .side_head .center_logo {
        display: none;
    }

    .user-dropdown {
        font-size: 14px;
        padding: 5px 14px 5px 10px;
    }

    .top_block {
        margin-top: -25px;
    }

    .img_description {
        font-size: 18px;
        line-height: 22px;
    }

    .html_wrap {
        figcaption {
            font-size: 18px;
            line-height: 22px;
        }
    }

    .library-tab-content {
        .html_wrap {
            margin-top: 15px;
        }
    }

    .wrapper_line {
        padding: 50px 0 90px 0;
    }

    .frame_ {
        width: 245px !important;
        height: 245px;
    }

    .carousel-arrow {
        width: 41px;
    }

    .carousel-arrow.right-arrow {
        right: 5px;
    }

    .carousel-arrow.left-arrow {
        left: 5px;
    }

    .buttons_wrap {
        bottom: 8%;
    }

    .carousel-container .carousel-item .carousel-content .carousel-title {
        font-size: 18px;
        line-height: 20px;
        margin-bottom: 0;
    }

    .m_btn.wide {
        margin-top: 25px;
        margin-bottom: 0;
    }

    .carousel-container .carousel-item .carousel-content {
        bottom: 95px;
        width: 250px;
    }

    .m_btn {
        width: 233px;
        height: 72px;
    }

    .def-block_block-wrap {
        padding: 50px 0;
    }

    .secnd_block-wrap {
        padding: 50px 0;
    }

    .secnd_block-wrap .row_btns {
        padding: 0 10px;
        max-width: 100%;
    }

    .ms_wrap {
        margin: 12px 0 0 40px;
    }

    .quick-links .link-icons {
        padding: 0;
    }

    .footer_wrap .nav_wrap {
        padding-bottom: 40px;
    }

    dialog.stylized_wide {
        height: fit-content !important;
    }

    .dropdown-header {
        height: 36px !important;
    }

    .dropdown_c {
        font-size: 14px !important;
        line-height: 14px !important;
    }

    .search-input {
        height: 24px !important;
        font-size: 14px !important;
        line-height: 14px !important;

        &::placeholder {
            font-size: 14px !important;
            line-height: 14px !important;
        }
    }

    .srchh {
        height: 12px !important;
        width: 12px !important;
        bottom: 7px !important;
    }

    .dropdown-item:not(.cat_i) {
        min-height: 30px !important;
        padding: 8px !important;
    }

    input.item_ {
        width: 12px !important;
        height: 12px !important;
    }

    p.item_ {
        width: 12px !important;
        height: 12px !important;
    }

    .playControls__control.playControl,
    .playControls__control:not(:first-child).playControl {
        margin-left: 20px;
    }

    .checked {
        background-size: 12px 12px !important;
    }

    .selectd_ {
        p.item_ {
            background-size: 12px 12px !important;
        }
    }

    .search-input {
        width: calc(100% - 10px) !important;
        margin: 0 5px !important;
    }

    .dropdown-menu {
        padding: 4px 0 !important
    }

    .ent-mob_switch {
        bottom: 11%;
    }

    .search-wrap {
        margin-top: 21% !important;
        justify-content: unset !important;
    }
}

@media (max-width: 420px) {
    .dec_head._background {
        background-size: 100% !important;
        padding: 62px 0 50px 0 !important;
        min-height: 360px;
        margin: -100px 0 -20px 0 !important;
        justify-content: unset !important;
    }

    .cat_wrap {
        margin: -220px auto 20px auto !important;
    }
}

@media (max-width: 400px) {
    .one_of_ {
        margin-right: 20px !important;
    }

    .side_wrap {
        margin-right: 20px !important;

        &.last_one {
            margin-right: 0 !important;
            margin-left: 20px !important;
        }
    }

    .cat_wrap {
        margin: -230px auto 20px auto !important;
    }

    .quick-links .link-icons.complex {
        padding: 0;
    }

    .m_btn.mid {
        height: 42px;
    }

    .quick-links .link-icons .link-item span {
        margin-top: 12px;
    }

    .quick-links .link-icons .link-item {
        width: 140px;
    }

    .carousel-arrow.right-arrow {
        right: -5px;
    }

    .carousel-arrow.left-arrow {
        left: -5px;
    }

    .m_btn.mid {
        margin-bottom: 60px;
        margin-top: 10px;
    }

    .sidebar_cl a {
        padding: 15px 0px;
    }

    .secnd_block-wrap .row_btns {
        padding: 0;
    }
}

@media (max-width: 370px) {
    .center_menu {
        width: 60px !important;
        height: 60px !important;
        top: -14px !important;

        span {
            font-size: 12px !important;
        }
    }

    .side_item {
        width: 60px !important;

        span {
            font-size: 8px !important;
            letter-spacing: 1px !important;
        }
    }

    .add-menu_wrap {
        height: 45px !important;
    }

    .cat_wrap {
        margin: -240px auto 20px auto !important;
    }

    .ent-mob_switch {
        min-width: 295px;
    }

    .def-block_block-wrap.nxt_ {
        padding: 260px 0 10px 0;
    }

    .item_menu {
        font-size: 20px;
        line-height: 25px;
        margin-bottom: 18px;
    }

    .side_ {
        padding: 10px 10px 46px 18px;
    }

    .sidebar_cl .button_img a {
        width: 135px;
        height: 30px;
        font-size: 12px;
    }

    .m_btn.light_ {
        scale: .9;
    }

    .footer_wrap {
        padding: 55px 15px 240px 15px !important;
    }
}

@media (max-width: 350px) {
    .cat_wrap {
        margin: -250px auto 20px auto !important;
    }
}