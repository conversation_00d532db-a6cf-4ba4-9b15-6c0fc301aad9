import 'zone.js/node';
import { fileURLToPath } from 'node:url';
import { dirname, resolve } from 'node:path';
import { AngularNodeAppEngine, createNodeRequestHandler, isMainModule, writeResponseToNodeResponse } from '@angular/ssr/node';
import express from 'express';
import dotenv from 'dotenv';

dotenv.config();

export function app(): express.Express {
  const server = express();
  const serverDistFolder = dirname(fileURLToPath(import.meta.url));
  const browserDistFolder = resolve(serverDistFolder, '../browser');

  const angularNodeAppEngine = new AngularNodeAppEngine();

  server.get(
    '**',
    express.static(browserDistFolder, {
      maxAge: '1y',
      index: 'index.html',
    })
  );

  // All regular routes use the Angular engine
  server.get('*', (req, res, next) => {
    angularNodeAppEngine
      .handle(req, { server: 'express' })
      .then((response) => (response ? writeResponseToNodeResponse(response, res) : next()))
      .catch(next);
  });

  return server;
}

const server = app();

if (isMainModule(import.meta.url)) {
  const port = process.env['CLIENT_PORT'] ? parseInt(process.env['CLIENT_PORT']) : 4000;
  const host = process.env['HOST'] || 'localhost';
  server.listen(port, host, () => {
    console.log(`Node Express server listening on http://${host}:${port}`);
  });
}

console.warn('Node Express server started');

// This exposes the RequestHandler
export default createNodeRequestHandler(server);
