{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"client": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/client", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss", "src/assets/styles/responsive.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5MB", "maximumError": "10MB"}, {"type": "anyComponentStyle", "maximumWarning": "5kB", "maximumError": "200kB"}], "outputHashing": "all", "server": "src/main.server.ts", "prerender": {"routesFile": "routes.txt"}, "ssr": {"entry": "server.ts"}, "outputMode": "server"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "tsConfig": "tsconfig.app.development.json"}, "development-ssr": {"optimization": false, "extractLicenses": false, "sourceMap": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "server": "src/main.server.ts", "outputMode": "server", "ssr": {"entry": "server.dev.ts"}}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "outputHashing": "all"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "client:build:production"}, "development": {"buildTarget": "client:build:development"}, "local": {"buildTarget": "client:build:local"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss", "src/assets/styles/responsive.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}