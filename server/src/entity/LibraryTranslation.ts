import { AudioTag } from "@/entity/AudioTag"
import { Library } from "@/entity/Library"
import { User } from "@/entity/User"
import {
    BaseEntity,
    Column, CreateDateColumn,
    Entity,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn
} from "typeorm"
import { LibraryQuote } from './LibraryQuote'

@Entity()
export class LibraryTranslation extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @CreateDateColumn()
    created_at: Date;

    @Column()
    external_id: string;

    @Column()
    lang: string;

    @Column()
    code: string

    @Column()
    title: string

    @Column({nullable: true})
    author: string;

    @Column({nullable: true})
    reader: string;

    @Column()
    seo_title: string

    @Column()
    seo_description: string

    @Column({nullable: true})
    pages: string

    @Column({nullable: true})
    category: string

    @Column({nullable: true})
    recomendation: string

    @Column({nullable: true})
    image: string

    @Column({nullable: true})
    image_mobile: string

    @Column({nullable: true})
    access: string

    @Column({nullable: true})
    annotation: string

    @Column({nullable: true})
    content: string

    @Column({nullable: true})
    format: string

    @Column({nullable: true})
    link: string

    @Column({nullable: true})
    summary: string

    @Column({nullable: true, type: 'json', default: []})
    audio: string

    @ManyToMany(() => AudioTag, tag => tag.audios)
    @JoinTable()
    tags: AudioTag[];

    @ManyToOne(() => Library, library => library.translations, {cascade: true, onUpdate: 'CASCADE', onDelete: 'CASCADE'})
    library: LibraryTranslation;

    @Column({default: 0})
    views: number

    @ManyToMany(() => User)
    @JoinTable({name: 'library_likes'})
    likes: User[]

    @Column({nullable: true})
    tagsString: string

    @Column({nullable: true})
    linkShop: string

    @Column({nullable: true})
    linkShopOnline: string

    @Column({nullable: true})
    duration: string

    @Column({nullable: true, type: 'simple-json', default: []})
    chapters: []

    @Column({default: false})
    paid: boolean

    @Column({nullable: true})
    priceRub: number

    @Column({nullable: true})
    priceEur: number

    @ManyToMany(() => User, u => u.libraryPurchases)
    @JoinTable()
    userPurchases: User[]

    @OneToMany(() => LibraryQuote, (quote) => quote.library, { cascade: true })
    quotes: LibraryQuote[];
}