import { AudioFavourite } from "@/entity/AudioFavourite"
import { AudioLike } from "@/entity/AudioLike"
import { AudioPosition } from "@/entity/AudioPosition"
import { Favourite } from "@/entity/Favourite"
import { File } from "@/entity/File"
import { ForumTopic } from "@/entity/ForumTopic"
import { ForumTopicCommentFavorite } from "@/entity/ForumTopicCommentFavorite"
import { ForumTopicCommentLike } from "@/entity/ForumTopicCommentLike"
import { ForumTopicFavorite } from "@/entity/ForumTopicFavorite"
import { LibraryQuote } from "@/entity/LibraryQuote"
import { LibraryTranslation } from "@/entity/LibraryTranslation"
import { Like } from "@/entity/Like"
import { Notification } from '@/entity/Notification'
import { NotificationDeleted } from '@/entity/NotificationDeleted'
import { NotificationRead } from '@/entity/NotificationRead'
import { PhotoLike } from '@/entity/PhotoLike'
import { Playlist } from "@/entity/Playlist"
import { UserAudioListened } from "@/entity/UserAudioListened"
import { UserSubscriptions } from '@/entity/UserSubscriptions'
import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"
import { ContentQuote } from "./ContentQuote"

export enum GroupsEnum {
    ADMIN = 'Администраторы сайта',
    VISITOR = 'Посетители',
    STUDENT = 'Ученики',
    NEW = 'Новые анкеты',

    PAGE_MODERATOR = 'Модератор страниц',
    TRANSLATION_MODERATOR = 'Модератор переводов',
    LIBRARY_MODERATOR = 'Модератор книг',
    PHOTO_MODERATOR = 'Модератор фото',
    LECTURE_MODERATOR = 'Модератор лекций',
    CONSTRUCTOR_MODERATOR = 'Модератор конструктора',
    USER_MODERATOR = 'Модератор пользователей',
    PERSONALPAGE_MODERATOR = 'Модератор личных страниц',
    FORUM_MODERATOR = 'Модератор форума',
    CALENDAR_MODERATOR = 'Модератор календаря',
    AUDIO_MODERATOR = 'Модератор аудио',
}

export enum Statuses {
    GUEST = 'Гость',
    NEW = 'Новичок',
    ZSVD = 'Заочный символ Веры Санатана Дхармы',
    OSVD = 'Очный символ Веры Санатана Дхармы',
    ZSVP = 'Заочный символ Веры Прибежища',
    OSVP = 'Очный символ Веры Прибежища', 
    GRIHASTHA = 'Грихастха',
    KARMA_SANNYASI = 'Карма-санньяси',
    VANAPRASHTHA = 'Ванапрастха',
    BRAHMACHARI = 'Брахмачари',
    SANNYASI = 'Санньяси',
}

@Entity('users')
export class User extends BaseEntity {
    @PrimaryGeneratedColumn()
    id?: number;

    @Column({default: true, type: 'boolean'})
    active: boolean;

    @Column({default: false, type: 'boolean'})
    confirmed: boolean;

    // Дата регистрации
    @CreateDateColumn({ type: "timestamp", name: 'created_at' })
    createdAt?: Date

    // Дата последнего изменения
    @UpdateDateColumn({ type: "timestamp", name: 'updated_at'  })
    updatedAt?: Date

    // Дата последней авторизации
    @Column({type: "timestamp", name: 'last_login_at', default: () => "CURRENT_TIMESTAMP"})
    lastLoginAt?: Date


    //E-mail
    @Column({ type: 'varchar' })
    email!: string

    //Пароль
    @Column({ type: 'varchar', select: false })
    password!: string

    @Column({type: 'simple-array', default: 'NEW'})
    groups!: GroupsEnum[];

    @Column({type: 'simple-array', default: 'NEW'})
    statuses!: Statuses[];

    //Имя
    @Column({ name: 'first_name', type: 'varchar', nullable: true })
    firstName!: string | null;

    //Фамилия
    @Column({ name: 'last_name', type: 'varchar', nullable: true })
    lastName!: string | null;

    //Отчество
    @Column({ name: 'middle_name', type: 'varchar',  nullable: true })
    middleName?: string | null

    // Духовное имя
    @Column({ name: 'spiritual_name', type: 'varchar', nullable: true })
    spiritualName?: string | null;

    // Дата рождения
    @Column({ name: 'birthdate', type: 'date', nullable: true })
    birthDate?: Date | null;

    // Страна
    @Column({ name: 'country', type: 'varchar', length: 100, nullable: true })
    country?: string;

    // Город
    @Column({ name: 'city', type: 'varchar', length: 100, nullable: true })
    city?: string;

    // Адрес
    @Column({ name: 'address', type: 'varchar', length: 255, nullable: true })
    address?: string;

    // Телефон
    @Column({ name: 'phone', type: 'varchar', length: 20, nullable: true })
    phone?: string;

    // Фотография
    @Column({ name: 'photo', type: 'varchar', length: 255, nullable: true })
    photo?: string;

    // Образование
    @Column({
        name: 'education',
        type: 'enum',
        enum: [
            'не выбрано',
            'среднее',
            'средне-специальное',
            'неоконченное высшее',
            'высшее',
        ],
        default: 'не выбрано',
    })
    education?: string;

    // Специальность
    @Column({ name: 'speciality', type: 'varchar', length: 255, nullable: true })
    speciality?: string;

    // Профессия
    @Column({ name: 'profession', type: 'varchar', length: 255, nullable: true })
    profession?: string;

    // Навыки
    @Column({ name: 'skills', type: 'text', nullable: true })
    skills?: string;

    // Служение
    @Column({ name: 'service', type: 'text', nullable: true })
    service?: string;

    @OneToMany(() => AudioLike, likes => likes.user)
    likes: AudioLike[];

    @OneToMany(() => AudioFavourite, favourite => favourite.user)
    favourites: AudioFavourite[];

    @Column({nullable: true})
    telegram?: string

    @OneToMany(() => AudioPosition, pos => pos.user)
    audioPositions: AudioPosition[];

    @OneToMany(() => Playlist, playlist => playlist.user)
    @JoinColumn({name: 'playlist_id'})
    playlists: Playlist[];

    @ManyToMany(() => LibraryTranslation, libraryFavourite => libraryFavourite.id)
    @JoinTable({name: 'library_favourites'})
    libraryFavourites: LibraryTranslation[];

    @ManyToMany(() => File, file => file.id)
    @JoinTable({name: 'photo_favourites'})
    photoFavourites: File[];

    @ManyToMany(() => LibraryTranslation, libraryTranslation => libraryTranslation.id)
    @JoinTable({name: 'library_likes'})
    libraryLikes: LibraryTranslation[];

    @ManyToMany(() => LibraryQuote, libraryQuote => libraryQuote.id)
    @JoinTable({name: 'library_quote_user'})
    quoteFavourites: LibraryQuote[];

    @ManyToMany(() => ContentQuote, contentQuote => contentQuote.id)
    @JoinTable({name: 'content_quote_user'})
    quoteFavouritesContent: ContentQuote[];

    @OneToMany(() => UserAudioListened, listened => listened.user)
    audioListened: UserAudioListened[];

    @OneToMany(() => Favourite, favourite => favourite.user)
    favouriteContent: Favourite[];

    @OneToMany(() => Like, favourite => favourite.user)
    likesContent: Like[];

    @OneToMany(() => ForumTopic, forumTopic => forumTopic.user)
    forumTopics: ForumTopic[];

    @OneToMany(() => ForumTopicCommentLike, forumTopic => forumTopic.user)
    topicLikes: ForumTopicCommentLike[];

    @OneToMany(() => ForumTopicFavorite, forumTopic => forumTopic.user)
    topicFavorites: ForumTopicFavorite[];

    @OneToMany(() => ForumTopicCommentFavorite, forumTopic => forumTopic.user)
    topicCommentFavorites: ForumTopicCommentFavorite[];

    @OneToOne(() => File)
    @JoinColumn()
    avatar: File

    @Column({nullable: true})
    language: string

    @Column({nullable: true, type: 'date'})
    dpDate: Date | null

    @Column({nullable: true})
    dpPractice: string

    @Column({nullable: true})
    dpLevel: string

    @Column({nullable: true})
    dpEvents: string

    @Column({nullable: true})
    dpMeet: string

    @Column({nullable: true})
    dpGoal: string

    @Column({nullable: true})
    health: string;

    @Column({nullable: true})
    supportInstructor: string;

    @Column({nullable: true})
    supportConsultation: string;

    @Column({nullable: true})
    supportCorrespondence: string;

    @Column({nullable: true})
    agree: boolean

    @Column({nullable: true})
    comment: string

    @UpdateDateColumn({nullable: true})
    lastActivity: Date;

    @OneToMany(() => PhotoLike, (like) => like.user)
    photoLikes: PhotoLike[];

    @OneToMany(() => UserSubscriptions, sub => sub.user)
    subscriptions: UserSubscriptions[];

    @ManyToMany(() => LibraryTranslation, l => l.userPurchases)
    libraryPurchases: LibraryTranslation[];

    @Column({nullable: true})
    dpHealth: string

    @Column({default: () => "CURRENT_TIMESTAMP", type: 'timestamp'})
    notifyViwedAt: Date;

    @OneToMany(() => Notification, notification => notification.user)
    notifications: Notification[];

    @OneToMany(() => NotificationRead, notificationRead => notificationRead.user)
    readNotifications: NotificationRead[];

    @OneToMany(() => NotificationDeleted, notificationDeleted => notificationDeleted.user)
    deletedNotifications: NotificationDeleted[];
}
