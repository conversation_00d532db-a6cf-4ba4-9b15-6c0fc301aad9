import {
  Base<PERSON>ntity,
  Column,
  CreateDateColumn,
  <PERSON>tity,
  PrimaryGeneratedColumn
} from 'typeorm'

export enum BackupStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

@Entity({ name: 'backups', synchronize: false })
export class Backup extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({
    type: 'enum',
    enum: BackupStatus,
    default: BackupStatus.PENDING
  })
  status: BackupStatus;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true, name: 'file_path' })
  filePath: string;

  @Column({ type: 'bigint', nullable: true, name: 'file_size' })
  fileSize: number;

  @Column({ type: 'text', nullable: true, name: 'error_message' })
  errorMessage: string;
}
