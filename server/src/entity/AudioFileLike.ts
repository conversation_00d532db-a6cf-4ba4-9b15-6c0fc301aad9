import {BaseEntity, Entity, ManyToOne, PrimaryGeneratedColumn} from "typeorm";
import {AudioFile} from "@/entity/AudioFile";
import {User} from "@/entity/User";

@Entity()
export class AudioFileLike extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  user: User;

  @ManyToOne(() => AudioFile, audioFile => audioFile.likes)
  audio: AudioFile;
}