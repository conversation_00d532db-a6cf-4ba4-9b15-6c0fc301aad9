import { BaseEntity, Column, CreateDateColumn, Entity, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Subscriptions, SubscriptionType } from '@/common/subscription/subscription.constants';
import { User } from '@/entity/User';

@Entity()
export class UserSubscriptions extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn()
  createdAt: Date;

  @Column()
  type: SubscriptionType

  @Column()
  paymentId: string

  @Column({ default: false })
  isAutoRenew: boolean;

  @Column({ type: 'timestamp', nullable: true })
  currentPeriodEnd: Date;

  @Column({ nullable: true })
  provider: 'stripe' | 'yookassa';

  @Column({ nullable: true })
  stripeSubscriptionId: string;

  @Column({ nullable: true })
  yookassaPaymentMethodId: string;

  @ManyToOne(() => User, (user) => user.subscriptions)
  user: User;
}