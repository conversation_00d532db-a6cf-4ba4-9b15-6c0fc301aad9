import { <PERSON><PERSON><PERSON>ty, Column, CreateDateColumn, Entity, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { NotificationDeleted } from './NotificationDeleted'
import { NotificationRead } from './NotificationRead'
import { User } from './User'

export enum NotificationType {
	CONTENT_COURSE_PUBLISHED = 'content.course.published',
	CONTENT_AUDIO_LECTURE_PUBLISHED = 'content.audio_lecture.published',
	CONTENT_BOOK_PUBLISHED = 'content.book.published',
	CONTENT_ARTICLE_PUBLISHED = 'content.article.published',
	CONTENT_NEWS_PUBLISHED = 'content.news.published',
	CONTENT_PREMIUM_ADDED = 'content.premium.added',
	FORUM_TOPIC_CREATED_ACTIVITY = 'forum.topic.created_activity',
	FORUM_POST_REPLY = 'forum.post.reply',
	FORUM_POST_LIKE = 'forum.post.like',
	FORUM_POST_FAVORITED = 'forum.post.favorited'
}

@Entity()
export class Notification extends BaseEntity {
	@PrimaryGeneratedColumn()
	id: number

	@CreateDateColumn()
	createdAt: Date

	@Column({
		type: 'enum',
		enum: Object.values(NotificationType)
	})
	type: NotificationType

	@Column({nullable: true})
	title: string

	@Column({nullable: true})
	link: string

	@ManyToOne(() => User, user => user.notifications, {onDelete: 'CASCADE'})
	user: User

	@OneToMany(() => NotificationRead, notificationRead => notificationRead.notification)
	readByUsers: NotificationRead[]

	@OneToMany(() => NotificationDeleted, notificationDeleted => notificationDeleted.notification)
	deletedByUsers: NotificationDeleted[]
}