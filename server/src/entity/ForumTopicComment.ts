import { ForumTopic } from "@/entity/ForumTopic"
import { ForumTopicCommentFavorite } from "@/entity/ForumTopicCommentFavorite"
import { ForumTopicCommentLike } from "@/entity/ForumTopicCommentLike"
import { User } from "@/entity/User"
import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from 'typeorm'

@Entity()
export class ForumTopicComment extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number

    @CreateDateColumn({ name: 'created_at' }) createdAt: Date;
    @UpdateDateColumn({ name: 'updated_at' }) updatedAt: Date;

    @Column()
    comment: string

    @OneToMany(() => ForumTopicComment, c => c.replyComment, { onDelete: 'CASCADE' })
    reply: ForumTopicComment;

    @ManyToOne(() => ForumTopicComment, c => c.reply, { onDelete: 'CASCADE' })
    @JoinColumn()
    replyComment: ForumTopicComment;

    @ManyToOne(() => ForumTopic, topic => topic.comments, {onDelete: 'CASCADE' })
    @JoinColumn()
    topic: ForumTopic;

    @OneToMany(() => ForumTopicCommentLike, comment => comment.topicComment)
    likes: ForumTopicCommentLike[];

    @OneToMany(() => ForumTopicCommentFavorite, comment => comment.topicComment)
    favorites: ForumTopicCommentFavorite[];

    @ManyToOne(() => User, user => user.forumTopics, { onDelete: 'CASCADE' })
    user: User;
}