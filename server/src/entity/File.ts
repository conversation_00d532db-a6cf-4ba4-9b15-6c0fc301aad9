import { Advertising } from "@/entity/Advertising"
import { Content } from "@/entity/Content"
import { PhotoLike } from '@/entity/PhotoLike'
import { PhotoTranslation } from "@/entity/PhotoTranslation"
import { BaseEntity, Column, Entity, OneToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm'

@Entity()
export class File extends BaseEntity{
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  name: string

  @Column({nullable:true})
  originalName: string

  @Column({nullable: true})
  description: string

  // @ManyToOne(() => Photo, (photo) => photo.photos, { onDelete: 'CASCADE' })
  // @JoinColumn()
  // photo: Photo

  // Один файл может быть связан с несколькими переводами
  @OneToMany(() => PhotoTranslation, (translation) => translation.cover)
  photoTranslations: PhotoTranslation[];

  @Column({default: 1})
  sort: number

  @OneToOne(() => Content, content => content.preview)
  content: Content;

  // @OneToOne(() => Content, content => content.preview_mobile)
  // content_mobile: Content;

  @OneToOne(() => Advertising, adv => adv.image)
  advertising: Content;

  @OneToMany(() => PhotoLike, (like) => like.file)
  photoLikes: PhotoLike[];
}