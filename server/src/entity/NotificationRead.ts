import { BaseEntity, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { User } from './User'
import { Notification } from './Notification'

@Entity('notification_read')
export class NotificationRead extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number

  @ManyToOne(() => User, user => user.readNotifications, { onDelete: 'CASCADE' })
  user: User

  @ManyToOne(() => Notification, notification => notification.readByUsers, { onDelete: 'CASCADE' })
  notification: Notification

  @CreateDateColumn()
  readAt: Date
}
