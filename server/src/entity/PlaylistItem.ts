import { BaseEntity, Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Playlist } from './Playlist';
import { Audio } from './Audio';
import { AudioFile } from './AudioFile';

@Entity()
export class PlaylistItem extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Playlist, playlist => playlist.items, { onDelete: 'CASCADE' })
  playlist: Playlist;

  @ManyToOne(() => Audio, { nullable: true, eager: true })
  audio: Audio;

  @ManyToOne(() => AudioFile, { nullable: true, eager: true, onDelete: 'CASCADE' })
  audioFile: AudioFile;

  @Column()
  order: number;
}