import { BaseEntity, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { User } from './User'
import { Notification } from './Notification'

@Entity('notification_deleted')
export class NotificationDeleted extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number

  @ManyToOne(() => User, user => user.deletedNotifications, { onDelete: 'CASCADE' })
  user: User

  @ManyToOne(() => Notification, notification => notification.deletedByUsers, { onDelete: 'CASCADE' })
  notification: Notification

  @CreateDateColumn()
  deletedAt: Date
}
