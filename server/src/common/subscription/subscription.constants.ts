export const Subscriptions = {
  AUDIO: {
    name: 'Аудио',
    price: {
      rub: 1200,
      eur: 12
    },
    components: ['AUDIO'] as const,
    stripePriceId: 'price_1RtlAj1ElDUyO74hsup1kgxf'
  },
  LIBRARY: {
    name: 'Библиотека',
    price: {
      rub: 1500,
      eur: 15
    },
    components: ['LIBRARY'] as const,
    stripePriceId: 'price_1RtlDc1ElDUyO74h27j5I2x0'
  },
  COURSES: {
    name: 'Курсы',
    price: {
      rub: 2500,
      eur: 25
    },
    components: ['COURSES'] as const,
    stripePriceId: 'price_1RtlEy1ElDUyO74hhVQMx1YL'
  },
  AUDIO_AND_LIBRARY: {
    name: 'Аудио + Библиотека',
    price: {
      rub: 2200,
      eur: 22
    },
    components: ['AUDIO', 'LIBRARY'] as const,
    stripePriceId: 'price_1RtlFZ1ElDUyO74hDq7wl6Q4'
  },
  AUDIO_AND_COURSES: {
    name: 'Аудио + Курсы',
    price: {
      rub: 3200,
      eur: 32
    },
    components: ['AUDIO', 'COURSES'] as const,
    stripePriceId: 'price_1RtlG61ElDUyO74httzgkYWP'
  },
  LIBRARY_AND_COURSES: {
    name: 'Библиотека + Курсы',
    price: {
      rub: 3500,
      eur: 35
    },
    components: ['LIBRARY', 'COURSES'] as const,
    stripePriceId: 'price_1RtlGn1ElDUyO74h4fRdkono'
  },
  FULL_ACCESS: {
    name: 'Полный доступ',
    price: {
      rub: 3900,
      eur: 39
    },
    components: ['AUDIO', 'LIBRARY', 'COURSES'] as const,
    stripePriceId: 'price_1RtlHK1ElDUyO74hP6dQrsDH'
  },
} as const

export type SubscriptionType = keyof typeof Subscriptions;