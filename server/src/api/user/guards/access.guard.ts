import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GroupsEnum } from '@/entity/User'

@Injectable()
export class AccessGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredGroups = this.reflector.getAllAndOverride<GroupsEnum[]>('groups', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredGroups) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();

    if (!user || !user.groups) {
      return false;
    }

    if (user.groups.includes('ADMIN')) {
      return true;
    }

    return requiredGroups.some((group) => user.groups.includes(group));
  }
}