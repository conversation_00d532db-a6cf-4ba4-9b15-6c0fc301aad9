import { PaymentProviderType } from '@/api/client/donation/create-payment.dto'
import { IPaymentProvider } from '@/api/client/donation/donation.service'
import { AudioFavourite } from "@/entity/AudioFavourite"
import { Notification } from '@/entity/Notification'
import { NotificationDeleted } from '@/entity/NotificationDeleted'
import { NotificationRead } from '@/entity/NotificationRead'
import { Playlist } from '@/entity/Playlist'
import { GroupsEnum, Statuses, User } from "@/entity/User"
import { HttpService } from '@nestjs/axios'
import { BadRequestException, Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { hash, verify } from 'argon2'
import * as nodemailer from 'nodemailer'
import { IsNull } from 'typeorm'
import { FileService } from '../file/file.service'
import { SignInDto } from "./dto/sign.dto"

@Injectable()
export class UserService {
    constructor(
      private jwtService: JwtService,
      private fileService: FileService,
      private readonly httpService: HttpService,
      ) {}

    private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

    async getOne(id: number) {
        return await User.findOne({
            where: {id},
            relations: ['avatar', 'subscriptions']
        })
    }

    async getAll() {
        return await User.find({ relations: ['avatar', 'subscriptions'] });
    }

    async signIn(dto: SignInDto) {
        const user = await User.findOne({
            where: { email: dto.email },
            select: {
                id: true,
                password: true,
                firstName: true,
                active: true
            }
        });
        if(!user) throw new NotFoundException();
        const matchPassword = await verify(user.password, dto.password);
        if (!matchPassword) throw new UnauthorizedException();
        if(!user.active) throw new UnauthorizedException('Ваш профиль заблокирован администратором');
        return this.createAccessToken(user);
    }

    async signUp(dto: any, captcha = true) {
        const user = await User.findOneBy({ email: dto.email });
        if(user) throw new BadRequestException(`Данная почта уже зарегистрирована`);

        if(captcha) {
          const checkCaptcha = await this.checkCaptcha(dto.captcha);
          if(!checkCaptcha) throw new BadRequestException('Капча не пройдена');
        }

        return await User.save({
            ...dto,
            password: await hash(dto.password)
        })
    }

    createAccessToken(user: User) {
        return { accessToken: this.jwtService.sign({ id: user.id }), name: user.firstName };
    }

    async getById(id: number) {
        return await User.findOneBy({id})
    }

    async getByEmail(email: string) {
        return await User.findOneBy({email})
    }

    async update(id: number, form: any) {
        delete form.subscriptions
        
        const user = await User.findOne({
            where: {id},
            relations: ['avatar'],
        })

        if(form.avatar && !form.avatar.id) {
            form.avatar = await this.fileService.save(form.avatar.originalName, 'avatar', form.avatar.name, user.avatar)
        }

        return await User.update(id, form);
    }
    async getProfile(id: number, updateActivity: boolean = true) {
        if(updateActivity) {
            User.update(id, {lastActivity: new Date()})
        }

        const user = await User.findOne({
            where: {id},
            relations: ['avatar', 'subscriptions', 'libraryPurchases']
        })

        const allNotifications = await Notification.find({
            where: [
                { user: { id: user.id } },
                { user: IsNull() }
            ],
            select: ['id', 'user']
        })

        const deletedNotifications = await NotificationDeleted.find({
            where: { user: { id: user.id } },
            select: ['notification'],
            relations: ['notification']
        })

        const deletedNotificationIds = deletedNotifications.map(dn => dn.notification.id);

        const visibleNotifications = allNotifications.filter(n => !deletedNotificationIds.includes(n.id));

        const readNotifications = await NotificationRead.find({
            where: { user: { id: user.id } },
            select: ['notification'],
            relations: ['notification']
        })

        const readNotificationIds = readNotifications.map(rn => rn.notification.id);

        const unreadNotificationsCount = visibleNotifications.filter(n => !readNotificationIds.includes(n.id)).length

        return {...user, unreadNotificationsCount};
    }
    async removeFromFavourites(id: number) {
        return await AudioFavourite.delete(id)
    }

    async getGroups() {
        return Object.keys(GroupsEnum).map(k => ({value: k, label: GroupsEnum[k]}))
    }

    async getStatuses() {
        return Object.keys(Statuses).map(k => ({value: k, label: Statuses[k]}))
    }

    async deleteUser(id: number) {
        return await User.delete(id)
    }

    async getPlaylist(userId: number) {
        const params = {
            where: { id: userId },
            relations: [
                'playlists',
                'playlists.items',
                'playlists.items.audio',
                'playlists.items.audio.likes',
                'playlists.items.audio.likes.user',
                'playlists.items.audioFile',
                'playlists.items.audioFile.likes',
                'playlists.items.audioFile.likes.user',
            ],
        }

        let user = await User.findOne(params);

        if (!user) return [];

        if(!user.playlists.some((e) => e.name === 'Послушать позже')) {
            await Playlist.save({
                name: 'Послушать позже',
                user
            })

            user = await User.findOne(params);
        }

        const transformedPlaylists = user.playlists.map(playlist => {

            const sortedItems = playlist.items.sort((a, b) => a.order - b.order);

            const transformedItems = sortedItems.map(item => {
                const track: any = item.audio || item.audioFile;

                if (!track) {
                    return null;
                }

                const likesCount = track.likes?.length || 0;
                const isLiked = track.likes?.some(like => like.user?.id === userId) || false;

                return {
                    ...track,
                    likes: likesCount,
                    liked: isLiked,
                    link: track?.link || track?.url
                };
            }).filter(Boolean);

            return {
                ...playlist,
                items: transformedItems,
            };
        });

        transformedPlaylists.sort((a, b) => {
            if (a.name === 'Послушать позже') return -1;
            if (b.name === 'Послушать позже') return 1;
            return 0;
        });

        return transformedPlaylists;
    }


    async checkCaptcha(token: string) {
        try {
            const {data} = await this.httpService.axiosRef.post('https://www.google.com/recaptcha/api/siteverify', 'secret=6LdHg4IrAAAAAAPGKq5GaLlgG3nig4_40FOxtHtv&response=' + token);
            return data.success
        } catch(e) {
            return false;
        }
    }

    async resetPassword(email: string) {
        const user = await User.findOneBy({email});
        if (!user) throw new UnauthorizedException('Пользователь не найден');

        const token = this.jwtService.sign({ id: user.id, email: user.email });

        const smtp = nodemailer.createTransport({
            service: 'yandex',
            auth: {
                user: "<EMAIL>",
                pass: "shiva108datta"
            }
        })

        const mailOptions = {
            from: '<EMAIL>',
            to: user.email,
            subject: 'Восстановление пароля',
            text: `Вы запросили восстановление пароля. Перейдите по следующей ссылке для его сброса: https://dev.advayta.org/ru/forgot?token=${token}`,
        }

        return await smtp.sendMail(mailOptions);
    }

    async changePassword(body: { token: string, password: string, confirmPassword: string }) {
        if(body.password !== body.confirmPassword) throw new BadRequestException('Пароли не совпадают');

        const { id, email } = this.jwtService.verify(body.token);
        const user = await User.findOneBy({id});
        if (!user) throw new UnauthorizedException('Пользователь не найден');

        return await User.update(id, {
            password: await hash(body.password)
        })
    }
}
