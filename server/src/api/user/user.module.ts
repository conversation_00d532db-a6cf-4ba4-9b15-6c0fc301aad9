import { GoogleStrategy } from "@/api/user/google.strategy"
import { Favourite } from "@/entity/Favourite"
import { Like } from "@/entity/Like"
import { Notification } from '@/entity/Notification'
import { NotificationDeleted } from '@/entity/NotificationDeleted'
import { NotificationRead } from '@/entity/NotificationRead'
import { User } from "@/entity/User"
import { UserGroup } from "@/entity/UserGroup"
import { UserSubscriptions } from '@/entity/UserSubscriptions'
import { HttpModule } from '@nestjs/axios'
import { Module } from '@nestjs/common'
import { JwtModule } from "@nestjs/jwt"
import { PassportModule } from "@nestjs/passport"
import { TypeOrmModule } from "@nestjs/typeorm"
import { FileService } from '../file/file.service'
import { JwtStrategy } from "./jwt.strategy"
import { UserController } from './user.controller'
import { UserService } from './user.service'

@Module({
  imports: [
      PassportModule,
      TypeOrmModule.forFeature([User, UserGroup, Favourite, Like, UserSubscriptions, Notification, NotificationRead, NotificationDeleted]),
      JwtModule.register({
        secret: 'advaytasecret',
        signOptions: {
          expiresIn: '1h'
        }
    }),
    HttpModule
  ],
  controllers: [UserController],
  providers: [UserService, JwtStrategy, GoogleStrategy, FileService],
})
export class UserModule {}
