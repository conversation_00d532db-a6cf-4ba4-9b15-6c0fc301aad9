import { Module } from '@nestjs/common';
import { AudiofilesService } from '@/api/firebase/audiofiles/audiofiles.service';
import { AudiofilesController } from './audiofiles.controller';
import { AudiofileTypesService } from '@/api/firebase/audiofile-types/audiofile-types.service';
import { AudiofileTagsService } from '@/api/firebase/audiofile-tags/audiofile-tags.service';
import { AudiofileSingersService } from '@/api/firebase/audiofile-singers/audiofile-singers.service';

@Module({
  controllers: [AudiofilesController],
  providers: [AudiofilesService, AudiofileTypesService, AudiofileTagsService, AudiofileSingersService],
})
export class AudiofilesModule {}
