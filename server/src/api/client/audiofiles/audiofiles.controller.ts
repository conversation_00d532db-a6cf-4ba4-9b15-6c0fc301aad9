import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AudiofilesService } from '@/api/firebase/audiofiles/audiofiles.service';
import { Auth } from '@/api/user/decorators/auth.decorator';
import { User } from '@/api/user/decorators/user.decorator';
import { OptionalJwtAuthGuard } from '@/api/user/guards/auth.optional.guard';

@Controller('client/audiofiles')
export class AudiofilesController {
  constructor(private readonly audiofilesService: AudiofilesService) {}

  @Get()
  @UseGuards(OptionalJwtAuthGuard)
  async findAll(
    @Query() query: any,
    @User() user: any
  ) {
    return await this.audiofilesService.findAll(query, user);
  }

  @Get('types')
  async getTypes() {
    return await this.audiofilesService.getTypes();
  }

  @Get('singers')
  async getSingers() {
    return await this.audiofilesService.getSingers();
  }

  @Get('tags')
  async getTags() {
    return await this.audiofilesService.getTags();
  }

  @Post('like')
  @Auth()
  async like(
    @Body('id') id: number,
    @Req() req: any
  ) {
    return await this.audiofilesService.like(id, req.user)
  }
}
