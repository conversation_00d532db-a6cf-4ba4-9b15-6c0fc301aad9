import { Body, Controller, Post } from '@nestjs/common';
import { DonationService } from './donation.service';
import { CreatePaymentDto } from './create-payment.dto';

@Controller('client/donation')
export class DonationController {
  constructor(private readonly donationService: DonationService) {}

  @Post('create-payment')
  createPaymentLink(@Body() body: CreatePaymentDto) {
    return this.donationService.createPayment(body);
  }
}