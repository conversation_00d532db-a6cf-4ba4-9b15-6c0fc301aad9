import { Modu<PERSON> } from '@nestjs/common';
import { DonationService } from './donation.service';
import { DonationController } from './donation.controller';
import { YookassaService } from './payment-providers/yookassa.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { StripeService } from './payment-providers/stripe.service';

@Module({
  imports: [ConfigModule, HttpModule],
  controllers: [DonationController],
  providers: [DonationService, YookassaService, StripeService],
})
export class DonationModule {}
