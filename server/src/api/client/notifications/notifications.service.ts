import { Notification } from '@/entity/Notification'
import { NotificationDeleted } from '@/entity/NotificationDeleted'
import { NotificationRead } from '@/entity/NotificationRead'
import { Injectable } from '@nestjs/common'
import { IsNull } from 'typeorm'

@Injectable()
export class NotificationsService {
	async getAll(userId: number) {
		const notifications = await Notification.find({
			where: [
				{ user: { id: userId } },
				{ user: IsNull() }
			],
			order: {
				createdAt: 'DESC'
			}
		})

		const deletedNotifications = await NotificationDeleted.find({
			where: { user: { id: userId } },
			select: ['notification'],
			relations: ['notification']
		})

		const deletedNotificationIds = deletedNotifications.map(dn => dn.notification.id);

		const visibleNotifications = notifications.filter(n => !deletedNotificationIds.includes(n.id));

		const readNotifications = await NotificationRead.find({
			where: { user: { id: userId } },
			select: ['notification'],
			relations: ['notification']
		})

		const readNotificationIds = readNotifications.map(rn => rn.notification.id);

		const unreadNotifications = visibleNotifications.filter(n => !readNotificationIds.includes(n.id));

		if (unreadNotifications.length > 0) {
			const readRecords = unreadNotifications.map(notification => ({
				user: { id: userId },
				notification: { id: notification.id }
			}));

			await NotificationRead.save(readRecords);
		}

		const notificationsWithReadStatus = visibleNotifications.map(notification => ({
			...notification,
			isRead: true
		}))

		return notificationsWithReadStatus
	}

	async markAsRead(userId: number, notificationId: number) {
		const existingRead = await NotificationRead.findOne({
			where: {
				user: { id: userId },
				notification: { id: notificationId }
			}
		})

		if (existingRead) {
			return { message: 'Уведомление уже отмечено как прочитанное' }
		}

		await NotificationRead.save({
			user: { id: userId },
			notification: { id: notificationId }
		})

		return { message: 'Уведомление отмечено как прочитанное' }
	}

	async markAllAsRead(userId: number) {
		const notifications = await Notification.find({
			where: [
				{ user: { id: userId } },
				{ user: IsNull() }
			],
			select: ['id']
		})

		const readNotifications = await NotificationRead.find({
			where: { user: { id: userId } },
			select: ['notification'],
			relations: ['notification']
		})

		const readNotificationIds = readNotifications.map(rn => rn.notification.id);

		const unreadNotifications = notifications.filter(n => !readNotificationIds.includes(n.id))

		if (unreadNotifications.length === 0) {
			return { message: 'Все уведомления уже прочитаны' }
		}

		const readRecords = unreadNotifications.map(notification => ({
			user: { id: userId },
			notification: { id: notification.id }
		}))

		await NotificationRead.save(readRecords)

		return { message: `Отмечено как прочитанные ${unreadNotifications.length} уведомлений` }
	}

	async deleteNotification(userId: number, notificationId: number) {
		const notification = await Notification.findOne({
			where: [
				{ id: notificationId, user: { id: userId } },
				{ id: notificationId, user: IsNull() }
			]
		})

		if (!notification) {
			throw new Error('Уведомление не найдено')
		}

		const existingDeleted = await NotificationDeleted.findOne({
			where: {
				user: { id: userId },
				notification: { id: notificationId }
			}
		})

		if (existingDeleted) {
			return { message: 'Уведомление уже удалено' }
		}

		if (notification.user) {
			await Notification.delete(notificationId)
			return { message: 'Уведомление удалено' }
		}

		await NotificationDeleted.save({
			user: { id: userId },
			notification: { id: notificationId }
		})

		return { message: 'Уведомление удалено' }
	}
}
