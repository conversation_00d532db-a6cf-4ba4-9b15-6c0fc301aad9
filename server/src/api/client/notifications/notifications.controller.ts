import { Auth } from '@/api/user/decorators/auth.decorator'
import { User } from '@/api/user/decorators/user.decorator'
import { Controller, Delete, Get, Param, Post } from '@nestjs/common'
import { NotificationsService } from './notifications.service'

@Controller('client/notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  @Auth()
  async getAll(@User() user: any) {
    return await this.notificationsService.getAll(user.id)
  }

  @Post(':id/read')
  @Auth()
  async markAsRead(@User() user: any, @Param('id') notificationId: number) {
    return await this.notificationsService.markAsRead(user.id, notificationId)
  }

  @Post('mark-all-read')
  @Auth()
  async markAllAsRead(@User() user: any) {
    return await this.notificationsService.markAllAsRead(user.id)
  }

  @Delete(':id')
  @Auth()
  async deleteNotification(@User() user: any, @Param('id') notificationId: number) {
    return await this.notificationsService.deleteNotification(user.id, notificationId)
  }
}
