import { Module } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { SubscriptionController } from './subscription.controller';
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service';
import { StripeService } from '@/api/client/donation/payment-providers/stripe.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [HttpModule],
  controllers: [SubscriptionController],
  providers: [SubscriptionService, YookassaService, StripeService],
})
export class SubscriptionModule {}
