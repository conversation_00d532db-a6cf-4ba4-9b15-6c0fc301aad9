import { Body, Controller, Get, Header, HttpCode, Post, Req, UseGuards } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';

@Controller('client/subscriptions')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get()
  async getList() {
    return this.subscriptionService.getList();
  }

  @Post('pay')
  @UseGuards(JwtAuthGuard)
  async paySubscription(@Req() req, @Body() body: any) {
    return await this.subscriptionService.pay(body, req.user)
  }

  @Post('paid')
  @HttpCode(200)
  async onSubscriptionPaid(@Body() body: any) {
    return await this.subscriptionService.onSubscriptionPaid(body);
  }

  @Post('cancel-auto-renew')
  @UseGuards(JwtAuthGuard)
  async cancelAutoRenew(@Body() body: { subscriptionId: number }, @Req() req) {
    return this.subscriptionService.cancelAutoRenew(body.subscriptionId, req.user);
  }

}
