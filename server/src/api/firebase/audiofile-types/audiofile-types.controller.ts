import { Body, Controller, Delete, Get, Param, Patch, Post } from '@nestjs/common';
import { AudiofileTypesService } from './audiofile-types.service';

@Controller('firebase/audiofile-types')
export class AudiofileTypesController {
  constructor(private readonly audiofileTypesService: AudiofileTypesService) {}

  @Get()
  async findAll() {
    return []
  }

  @Post()
  async create(@Body() body: any) {
    return await this.audiofileTypesService.create(body);
  }

  @Patch(':key')
  async update(
    @Param('key') key: string,
    @Body() body: any
  ) {
    return await this.audiofileTypesService.update(key, body);
  }

  @Delete(':key')
  async remove(@Param('key') key: string) {
    return await this.audiofileTypesService.remove(key)
  }
}
