import { Injectable, NotFoundException } from '@nestjs/common';
import { AudioFileType } from '@/entity/AudioFileType';

@Injectable()
export class AudiofileTypesService {
  async create(body: any) {
    return await AudioFileType.save({ external_id: body.key, name: body.value });
  }

  async findOneBy<PERSON>ey(key: string) {
    return await AudioFileType.findOneBy({external_id: key})
  }

  async update(key: string, body: any) {
    const item = await this.findOneByKey(key);
    if(!item) throw new NotFoundException(`Could not find ${key}`);

    item.name = body.value;

    return await item.save();
  }

  async remove(key: string) {
    const item = await this.findOneByKey(key);
    if(!item) throw new NotFoundException(`Could not find ${key}`);

    return await item.remove();
  }
}
