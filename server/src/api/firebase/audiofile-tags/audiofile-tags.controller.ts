import { Body, Controller, Delete, Param, Patch, Post } from '@nestjs/common';
import { AudiofileTagsService } from './audiofile-tags.service';

@Controller('firebase/audiofile-tags')
export class AudiofileTagsController {
  constructor(private readonly audiofileTagsService: AudiofileTagsService) {}

  @Post()
  async create(@Body() body: any) {
    return await this.audiofileTagsService.create(body);
  }

  @Patch(':key')
  async update(
    @Param('key') key: string,
    @Body() body: any
  ) {
    return await this.audiofileTagsService.update(key, body);
  }

  @Delete(':key')
  async remove(@Param('key') key: string) {
    return await this.audiofileTagsService.remove(key)
  }
}
