import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { AudiofilesService } from './audiofiles.service';
import { OptionalJwtAuthGuard } from '@/api/user/guards/auth.optional.guard';
import { User } from '@/api/user/decorators/user.decorator';

@Controller('firebase/audiofiles')
export class AudiofilesController {
  constructor(private readonly audiofilesService: AudiofilesService) {}

  @Get()
  @UseGuards(OptionalJwtAuthGuard)
  async findAll(
    @Query() query: any,
    @User() user: any
  ) {
    return await this.audiofilesService.findAll(query, user);
  }

  @Post()
  async create(@Body() body: any) {
    return await this.audiofilesService.create(body);
  }

  @Patch(':key')
  async update(
    @Param('key') key: string,
    @Body() body: any
  ) {
    return await this.audiofilesService.update(key, body);
  }

  @Delete(':key')
  async remove(@Param('key') key: string) {
    return await this.audiofilesService.remove(key);
  }
}
