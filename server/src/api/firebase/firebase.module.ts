import { AudioService } from "@/api/admin/audio/audio.service"
import { AudioFile } from '@/entity/AudioFile'
import { AudioFileLike } from '@/entity/AudioFileLike'
import { AudioFileSinger } from '@/entity/AudioFileSinger'
import { AudioFileTag } from '@/entity/AudioFileTag'
import { AudioFileType } from '@/entity/AudioFileType'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { AudiofileSingersModule } from './audiofile-singers/audiofile-singers.module'
import { AudiofileTagsModule } from './audiofile-tags/audiofile-tags.module'
import { AudiofileTypesModule } from './audiofile-types/audiofile-types.module'
import { AudiofilesModule } from './audiofiles/audiofiles.module'
import { FirebaseController } from './firebase.controller'
import { FirebaseService } from './firebase.service'

@Module({
  imports: [
    TypeOrmModule.forFeature([AudioFile, AudioFileTag, AudioFileType, AudioFileSinger, AudioFileLike]), 
    AudiofilesModule, 
    AudiofileTypesModule, 
    AudiofileSingersModule, 
    AudiofileTagsModule],
  controllers: [FirebaseController],
  providers: [FirebaseService, AudioService],
})
export class FirebaseModule {}
