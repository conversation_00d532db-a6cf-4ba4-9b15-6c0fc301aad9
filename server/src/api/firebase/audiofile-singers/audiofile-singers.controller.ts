import { Body, Controller, Delete, Param, Patch, Post } from '@nestjs/common';
import { AudiofileSingersService } from './audiofile-singers.service';

@Controller('firebase/audiofile-singers')
export class AudiofileSingersController {
  constructor(private readonly audiofileSingersService: AudiofileSingersService) {}

  @Post()
  async create(@Body() body: any) {
    return await this.audiofileSingersService.create(body);
  }

  @Patch(':key')
  async update(
    @Param('key') key: string,
    @Body() body: any
  ) {
    return await this.audiofileSingersService.update(key, body);
  }

  @Delete(':key')
  async remove(@Param('key') key: string) {
    return await this.audiofileSingersService.remove(key)
  }
}
