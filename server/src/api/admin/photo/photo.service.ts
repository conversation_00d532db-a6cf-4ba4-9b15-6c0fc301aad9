import { FileService } from "@/api/file/file.service"
import { File } from "@/entity/File"
import { Photo } from "@/entity/Photo"
import { PhotoTranslation } from "@/entity/PhotoTranslation"
import { Injectable } from '@nestjs/common'
import { existsSync, rmSync } from "fs"
import slugify from "slugify"

@Injectable()
export class PhotoService {
    constructor(private readonly fileService: FileService) {}
    async get(id: number) {
        return await Photo.findOne({
            where: {id},
            relations: ['translations.photos', 'translations.cover'],
        })
    }
    async create(dto: any) {
        const items = []
        const ru = dto.find(e => e.lang === 'ru')
        let slug = ru && ru.form.title ? slugify(ru.form.title, {lower: true}) : ''
        for(let item of dto) {
            if(!item.form.title) continue;
            item = await this.saveFiles(item, slug)
            items.push(await PhotoTranslation.save({...item.form, lang: item.lang, slug}))
        }
        return await Photo.save({translations: items, slug})
    }
    async update(id: number, dto: any) {
        const ru = dto.find(e => e.lang === 'ru')
        let slug = ru && ru.form.title ? slugify(ru.form.title, {lower: true}) : ''
        for(let item of dto) {
            if(item.form.id) {
                let photoTranslation = await PhotoTranslation.findOne({
                    where: {id: item.form.id},
                    relations: ['photos', 'cover']
                })

                if(item.form.cover && !item.form.cover.id) {
                    item.form.cover = await this.fileService.save(item.form.cover.originalName, 'photo/' + slug, item.form.cover.name, photoTranslation.cover, item.form.cover.description)
                }

                if(item.form.cover && item.form.cover.id) {
                    await this.fileService.update(item.form.cover.id, {description: item.form.cover.description})
                }

                for(let photo of item.form.photos) {
                    if(photo.id) {
                        await this.fileService.update(photo.id, {description: photo.description})
                    }
                }

                const newPhotos = item.form.photos.filter(photo => !photo.id);
                const photos = [];

                for(let photo of newPhotos) {
                    photos.push(await this.fileService.save(photo.originalName, 'photo/' + slug, photo.name, null, photo.description))
                }

                photoTranslation.photos = [...(photoTranslation.photos || []), ...photos];
                item.form.photos = photoTranslation.photos;
                photoTranslation.cover = item.form.cover
                photoTranslation.title = item.form.title
                photoTranslation.seo_title = item.form.seo_title
                photoTranslation.seo_description = item.form.seo_description
                await photoTranslation.save()
            }
        }
        return dto
    }

    async saveFiles(item: any, slug: string, oldCover: any = null, oldPhotos: any = null) {
        const photos = []
        if(item.form.cover && !item.form.cover.id) {
            item.form.cover = await this.fileService.save(item.form.cover.originalName, 'photo/' + slug, item.form.cover.name, oldCover, item.form.cover.description)
        }
        if(item.form.photos) {
            if(oldPhotos) {
                for(let p of oldPhotos) {
                    if(existsSync('./upload/' + p.name)) {
                        rmSync('./upload/' + p.name, { recursive: true });
                    }
                }
            }

            for(let photo of item.form.photos) {
                photos.push(await this.fileService.save(photo.originalName, 'photo/' + slug, photo.name, null, photo.description))
            }
            item.form.photos = photos
        }
        return item
    }

    async deletePhoto(id: number) {
        return await File.delete(id)
    }

    async delete(id: number) {
        const item = await this.get(id);
        for(let t of item.translations) {
            if(existsSync(`./upload/photo/${t.slug}`)) {
                rmSync(`./upload/photo/${t.slug}`, { recursive: true });
            }
        }
        return await Photo.delete(id)
    }
}
