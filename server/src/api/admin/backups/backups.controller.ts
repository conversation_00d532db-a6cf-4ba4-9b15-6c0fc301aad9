import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { Response } from 'express'
import { BackupsService } from './backups.service'

@Controller('admin/backups')
export class BackupsController {
  constructor(private readonly backupsService: BackupsService) {}

  @Post()
  async createBackup(@Body() body: any) {
    try {
      return await this.backupsService.createBackup(body);
    } catch (error) {
      throw new HttpException(
        `Ошибка создания резервной копии: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadBackup(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any
  ) {
    try {
      if (!file) {
        throw new Error('Файл не был загружен');
      }

      return await this.backupsService.createBackupFromFile(file, body);
    } catch (error) {
      throw new HttpException(
        `Ошибка загрузки резервной копии: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async getBackups(@Query() query: any) {
    try {
      return await this.backupsService.getBackups(query);
    } catch (error) {
      throw new HttpException(
        `Ошибка получения списка резервных копий: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async getBackupById(@Param('id') id: number) {
    try {
      return await this.backupsService.getBackupById(id);
    } catch (error) {
      throw new HttpException(
        `Ошибка получения резервной копии: ${error.message}`,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  @Get(':id/download')
  async downloadBackup(
    @Param('id') id: number,
    @Res() res: Response,
  ) {
    try {
      const { filePath, fileName } = await this.backupsService.downloadBackup(id);
      
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Type', 'application/octet-stream');
      res.sendFile(filePath);
    } catch (error) {
      throw new HttpException(
        `Ошибка скачивания резервной копии: ${error.message}`,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  @Delete(':id')
  async deleteBackup(@Param('id') id: number) {
    try {
      await this.backupsService.deleteBackup(id);
      return { message: `Резервная копия ${id} удалена` };
    } catch (error) {
      throw new HttpException(
        `Ошибка удаления резервной копии: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/restore')
  async restoreBackup(@Param('id') id: number) {
    try {
      return await this.backupsService.restoreBackup(id);
    } catch (error) {
      throw new HttpException(
        `Ошибка восстановления резервной копии: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
