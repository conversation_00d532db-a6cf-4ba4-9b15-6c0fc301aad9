import { FileService } from "@/api/file/file.service"
import { Advertising } from "@/entity/Advertising"
import { Notification, NotificationType } from '@/entity/Notification'
import { Injectable } from '@nestjs/common'
import { existsSync, rmSync } from "fs"

@Injectable()
export class AdvertisingService {
    constructor(private readonly fileService: FileService) {}

    async getAll() {
        return await Advertising.find({
            relations: ['image'],
        })
    }

    async getOne(id: number) {
        return await Advertising.findOne({
            where: {id},
            relations: ['image'],
        })
    }

    async create(body: any) {
        let isNew = true;
        if(body.image) {
            let oldImage = null;
            if(body.id) {
                const advertising = await this.getOne(body.id);
                oldImage = advertising.image;
                isNew = false;
            }
            if(!body?.image?.id) {
              body.image = await this.fileService.save(body.image.originalName, 'advertising', body.image.name, oldImage)
            }

            if(body.image.id) {
                await this.fileService.update(body.image.id, {description: body.image.description})
            }
        }

        const advertising = await Advertising.save(body);

        if(isNew) {
            await Notification.save({
                type: NotificationType.CONTENT_NEWS_PUBLISHED,
                title: body.title,
                link: `/ru/news`
            })
        }

        return advertising
    }

    async delete(id: number) {
        const advertising = await this.getOne(id);
        if(advertising.image && existsSync(`./upload/${advertising.image.name}`)) {
            rmSync(`./upload/${advertising.image.name}`, {recursive: true})
        }
        return await Advertising.delete(id);
    }
}
