import { FileService } from "@/api/file/file.service"
import { ForumCategory } from "@/entity/ForumCategory"
import { Injectable } from '@nestjs/common'
import { existsSync, rmSync } from "fs"

@Injectable()
export class ForumService {
    constructor(private readonly fileService: FileService) {}

    async getCategories() {
        return await ForumCategory.find({
            order: {
                order: "ASC"
            },
            relations: ['topics.comments']
        });
    }

    async getCategory(id: number) {
        return await ForumCategory.findOne({
            where: {id}
        })
    }

    async addCategory(body: any) {
        if(body.icon) {
            body.icon = await this.fileService.save(body.icon.originalName, 'forum', body.icon.name, null, body.icon.description)
        }
        return await ForumCategory.save(body)
    }

    async updateCategory(id: number, body: any) {
        const category = await this.getCategory(id);
        if(body.icon && !body.icon.id) {
            body.icon = await this.fileService.save(body.icon.originalName, 'forum', body.icon.name, category.icon, body.icon.description);
        }

        if(body.icon && body.icon.id) {
            await this.fileService.update(body.icon.id, {description: body.icon.description})
        }
        return await ForumCategory.update(body.id, body)
    }

    async deleteCategory(id: number) {
        const category = await this.getCategory(id);
        if(category.icon && existsSync(`./upload/${category.icon.name}`)) {
            rmSync(`./upload/${category.icon.name}`, {recursive: true})
        }
        return await ForumCategory.delete(id)
    }

    async updateCategoryOrder(body) {
      return await Promise.all(body.categories.map(async item => {
        const category = await this.getCategory(item.id);
        category.order = item.order;
        return await category.save();
      }))
  }
}
