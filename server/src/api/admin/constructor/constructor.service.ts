import { FileService } from "@/api/file/file.service"
import { ConstructorBlock } from "@/entity/ConstructorBlock"
import { Injectable } from '@nestjs/common'
import { existsSync, rmSync } from "fs"

@Injectable()
export class ConstructorService {
    constructor(private readonly fileService: FileService) {}
    async get(id: number) {
        return await ConstructorBlock.findOne({
            where: {id}
        })
    }
    async getAll() {
        return await ConstructorBlock.find()
    }

    async create(body) {
        // Сохраняем новые изображения для всех элементов
        for(let i in body.items) {
            if (body.items[i].image && !body.items[i].image.id) {
                body.items[i].image = await this.fileService.save(
                    body.items[i].image.originalName,
                    'constructor',
                    body.items[i].image.name,
                    null,
                    body.items[i].image.description
                );
            }
        }

        for(let i in body.items) {
            if (body.items[i].image && body.items[i].image.id) {
                await this.fileService.update(body.items[i].image.id, {description: body.items[i].image.description})
            }
        }

        if(body.id) {
            const block: any = await ConstructorBlock.findOne({
                where: {id: body.id}
            });

            // Удаляем старые изображения, которые больше не используются
            if(block.items && block.items.length) {
                // Сначала собираем все новые имена файлов
                const newFileNames = body.items
                    .filter(item => item?.image?.name)
                    .map(item => item.image.name.split('/').pop());

                // Удаляем только те файлы, которых нет в новых элементах
                for(let i in block.items) {
                    if(block.items[i]?.image) {
                        const oldFileName = block.items[i].image.name.split('/').pop();
                        if(!newFileNames.includes(oldFileName) &&
                            existsSync('./upload/' + block.items[i].image.name)) {
                            rmSync('./upload/' + block.items[i].image.name, {recursive: true});
                        }
                    }
                }
            }

            return await ConstructorBlock.update(body.id, body);
        }
        return await ConstructorBlock.save(body);
    }

    async update(body: any) {
        const block = await ConstructorBlock.findOne({
            where: {id: body.id}
        })

        for(let i in body.items) {
            body.items[i].image = await this.fileService.save(body.items[i].image.originalName, 'constructor', body.items[i].image.name, body.items[i].image)
        }

        block.title = body.title
        block.items = body.items
        return await block.save()
    }
    async delete(id: number) {
        const block: any = await ConstructorBlock.findOne({
            where: {id}
        })

        if(block.items && block.items.length) {
            for(let i in block.items) {
                if(existsSync(`./upload/${block.items[i].image.name}`)) {
                    rmSync(`./upload/${block.items[i].image.name}`, {recursive: true})
                }
            }
        }

        return await ConstructorBlock.delete(id)
    }
}
