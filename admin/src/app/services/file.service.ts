import {inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class FileService {
  http = inject(HttpClient);

  upload(files: FileList, folder: string = 'unknown') {
    const formData = new FormData();
    formData.append('folder', folder);
    for(let i in Array.from(files)) {
      formData.append('file[]', files[i]);
      formData.append('name[]', encodeURIComponent(files[i].name));
    }
    return this.http.post('/file/upload', formData)
  }

  delete(id: number) {
    return this.http.delete(`/file/${id}`)
  }

  deleteByPath(path: string, folder: string) {
    return this.http.delete(`/file`, {params: {path, folder}})
  }

  changeDescription(id: number, description: string) {
    return this.http.patch(`/file/${id}`, {description})
  }

  changeSort(items: any) {
    return this.http.patch(`/file/sort`, items)
  }

  uploadToTempFolder(files: FileList) {
    const formData = new FormData();
    for(let i in Array.from(files)) {
      formData.append('file[]', files[i]);
      formData.append('name[]', encodeURIComponent(files[i].name));
    }
    return this.http.post('/file/upload/tmp', formData)
  }
}
