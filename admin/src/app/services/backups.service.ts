import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'


@Injectable({
  providedIn: 'root'
})
export class BackupsService {
  http = inject(HttpClient)

  get() {
    return this.http.get('/admin/backups')
  }

  create() {
    return this.http.post('/admin/backups', { description: 'Ручной запуск' });
  }

  delete(id: number) {
    return this.http.delete(`/admin/backups/${id}`)
  }

  restore(id: number) {
    return this.http.post(`/admin/backups/${id}/restore`, {})
  }

  import(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    return this.http.post('/admin/backups/upload', formData);
  }
}
