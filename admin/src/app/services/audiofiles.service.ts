import {inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class AudiofilesService {
  private http = inject(HttpClient);

  findAll(filters: any) {
    return this.http.get('/client/audiofiles', {params: filters});
  }

  getTypes() {
    return this.http.get('/client/audiofiles/types');
  }

  getSingers() {
    return this.http.get('/client/audiofiles/singers');
  }

  getTags() {
    return this.http.get('/client/audiofiles/tags');
  }

  getPlaylists() {
    return this.http.get('/admin/audio/playlists');
  }
}
