import {inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {tap} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ConstructorService {
  http = inject(HttpClient)
  items: any = []
  get(id: number) {
    return this.http.get('/admin/constructor/' + id)
  }
  getAll() {
    return this.http.get('/admin/constructor').pipe(
      tap((res: any) => this.items = res),
    )
  }

  create(items: any) {
    return this.http.post('/admin/constructor/create', items)
  }

  createBlock(body: any) {
    return this.http.post('/admin/constructor', body)
  }
  updateBlock(body: any) {
    return this.http.patch('/admin/constructor/' + body.id, body)
  }
  deleteBlock(id: number) {
    return this.http.delete(`/admin/constructor/${id}`).pipe(
      tap(() => this.getAll().subscribe())
    )
  }
  createItem(body: any) {
    return this.http.post('/admin/constructor/item', body)
  }
  updateItem(body: any) {
    return this.http.patch('/admin/constructor/item', body)
  }
  deleteItem(id: number) {
    return this.http.delete(`/admin/constructor/item/${id}`)
  }
}
