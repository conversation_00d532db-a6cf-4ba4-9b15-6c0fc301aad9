import {inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class AdvertisingService {
  http = inject(HttpClient)

  getAll() {
    return this.http.get('/admin/advertising')
  }

  create(body: any) {
    return this.http.post('/admin/advertising', body)
  }

  remove(id: number) {
    return this.http.delete(`/admin/advertising/${id}`)
  }
}
