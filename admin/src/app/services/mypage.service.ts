import {inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class MypageService {
  http = inject(HttpClient);

  getAll() {
    return this.http.get('/admin/mypage')
  }

  getOne(id: number | string) {
    return this.http.get(`/admin/mypage/${id}`)
  }

  create(body: any) {
    return this.http.post('/admin/mypage', body)
  }

  removePage(id: number) {
    return this.http.delete(`/admin/mypage/${id}`)
  }

}
