import {inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class ForumService {
  http = inject(HttpClient);

  getCategory(id: number) {
    return this.http.get(`/admin/forum/category/${id}`);
  }

  getCategories() {
    return this.http.get("/admin/forum/category");
  }

  addCategory(body: any) {
    return this.http.post('/admin/forum/category', body);
  }

  updateCategory(body: any) {
    return this.http.patch('/admin/forum/category/' + body.id, body);
  }

  deleteCategory(id: number) {
    return this.http.delete('/admin/forum/category/' + id);
  }

  updateCategoryOrder(orderData: { id: number, order: number }[]) {
    return this.http.post(`/admin/forum/category/order`, { categories: orderData });
  }
}
