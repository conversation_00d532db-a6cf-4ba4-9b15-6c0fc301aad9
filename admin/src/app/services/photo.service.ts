import {inject, Injectable} from '@angular/core';
import {Observable, tap} from "rxjs";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class PhotoService {
  photos: any = []
  private http = inject(HttpClient)

  get(id: number) {
    return this.http.get('/admin/photo/' + id)
  }

  getAll() {
    return this.http.get('/admin/photo').pipe(
      tap(res => this.photos = res)
    )
  }

  create(form: any) {
    return this.http.post('/admin/photo', form)
  }

  update(id: number, form: any) {
    return this.http.patch(`/admin/photo/${id}`, form)
  }

  delete(id: number) {
    return this.http.delete(`/admin/photo/${id}`).pipe(
      tap(() => this.getAll().subscribe())
    )
  }

  deletePhoto(id: number) {
    return this.http.delete(`/admin/photo/photos/${id}`)
  }

}
