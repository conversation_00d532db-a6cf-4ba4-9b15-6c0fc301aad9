<div class="admin-component">
  <!-- Admin Dialog Component -->
  <admin-dialog></admin-dialog>

  <!-- <PERSON> Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Новости</h1>
    </div>
    <div class="admin-actions">
      <button class="btn btn-primary" (click)="showDialog()">Добавить элемент</button>
    </div>
  </div>

  <!-- Content -->
  <div class="admin-content-wrapper">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th class="w-24">Изображение</th>
            <th>Заголовок</th>
            <th>Описание</th>
            <th>Тип</th>
            <th>Ссылка</th>
            <th>Частота</th>
            <th class="text-center w-64">Действия</th>
          </tr>
        </thead>
        <tbody>
          @for(item of items; track item.id) {
            <tr>
              <td>
                @if (item.image) {
                  <img class="w-20 h-20 object-cover rounded" [src]="environment.serverUrl + '/upload/' + item.image.name" alt="">
                }
              </td>
              <td>{{item.title}}</td>
              <td class="max-w-xs truncate" [title]="item.description">{{item.description}}</td>
              <td>
                @switch(item.type) {
                  @case("1") {
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Модалка</span>
                  }
                  @case("2") {
                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Баннер</span>
                  }
                }
              </td>
              <td>{{item.link}}</td>
              <td>{{item.freq}}</td>
              <td>
                <div class="admin-table-actions">
                  <button class="btn btn-sm btn-primary" (click)="edit(item)">Редактировать</button>
                  <button class="btn btn-sm btn-danger" (click)="remove(item.id)">Удалить</button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>


  <!-- Edit Dialog -->
  <dialog #dialog class="admin-modal">
    <div class="admin-modal-content">
      <h3 class="text-lg font-semibold mb-4">Редактировать элемент календаря</h3>
      <form [formGroup]="form" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center">
            <input formControlName="active" type="checkbox" class="form-checkbox mr-2">
            <label class="text-sm">Календарь</label>
          </div>
          <div class="flex items-center">
            <input formControlName="advertising" type="checkbox" class="form-checkbox mr-2">
            <label class="text-sm">Реклама</label>
          </div>
        </div>

        <div>
          <label class="admin-filter-label">Заголовок</label>
          <input type="text" class="form-input" formControlName="title">
        </div>

        <div>
          <label class="admin-filter-label">Тип</label>
          <select formControlName="type" class="form-select" [disabled]="!form.get('advertising')?.value">
            <option value="1">Модалка</option>
            <option value="2">Баннер</option>
          </select>
        </div>

        <div>
          <label class="admin-filter-label">Описание</label>
          <textarea formControlName="description" class="form-textarea"></textarea>
        </div>

        <div>
          <label class="admin-filter-label">Дата</label>
          <input type="date" formControlName="date" class="form-input" />
        </div>

        <div>
          <label class="admin-filter-label">Изображение</label>
          <input type="file" class="form-input" accept="image/*" (change)="uploadFile($event)">
          @if(form.value.image) {
            <PhotoPreview [disableRemoveBtn]="true" [items]="[form.value.image]"/>
          }
        </div>

        <div>
          <label class="admin-filter-label">Ссылка</label>
          <input type="text" class="form-input" formControlName="link">
        </div>

        <div>
          <label class="admin-filter-label">Количество показов {{form.value.freq}}</label>
          <input type="number" class="form-input" formControlName="freq" [disabled]="!form.get('advertising')?.value">
        </div>
      </form>

      <div class="admin-modal-footer">
        <button [disabled]="form.invalid" class="btn btn-primary" (click)="onSubmit()">Сохранить</button>
        <button class="btn btn-outline-secondary" (click)="this.closeDialog()">Закрыть</button>
      </div>
    </div>
  </dialog>
</div>
