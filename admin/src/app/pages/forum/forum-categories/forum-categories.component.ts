import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {ForumService} from "@/services/forum.service";

@Component({
    selector: 'app-forum-categories',
    imports: [
        RouterLink
    ],
    templateUrl: './forum-categories.component.html',
    styleUrl: './forum-categories.component.scss'
})
export class ForumCategoriesComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  message: string = "";
  forumService = inject(ForumService);
  router = inject(Router);
  categories: any = []

  get numCategoryComments() {
    return (topics: any) => topics.reduce((acc: any, cur: any) => acc + cur.comments.length, 0)
  }

  ngOnInit() {
    this.getCategories();
  }

  getCategories() {
    this.forumService.getCategories().subscribe((res: any) => this.categories = res);
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  deleteCategory(id: number) {
    this.openConfirmationDialog('Удалить категорию?').then((confirmed) => {
      if (confirmed) {
        this.forumService.deleteCategory(id).subscribe({
          next: () => {
            this.openModal('Удалена категория!')
            this.getCategories()
          },
          error: () => {
            this.openModal('Ошибка удаления, попробуйте еще раз')
            this.getCategories()
          }
        });
      }
    });
  }

  moveCategory(direction: 'up' | 'down', index: number) {
    const categories = [...this.categories];

    const itemToMove = categories[index];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    if (newIndex < 0 || newIndex >= categories.length) {
      return;
    }

    categories.splice(index, 1);
    categories.splice(newIndex, 0, itemToMove);

    this.categories = categories;

    const orderPayload = categories.map((cat, order) => ({
      id: cat.id,
      order
    }));

    this.forumService.updateCategoryOrder(orderPayload).subscribe();
  }
}
