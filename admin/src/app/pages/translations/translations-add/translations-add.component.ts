import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {LanguagesEnum} from "@/enums/languages.enum";
import {CommonModule} from "@angular/common";
import {FormArray, FormBuilder, ReactiveFormsModule, Validators} from "@angular/forms";
import {TranslationService} from "@/services/translation.service";
import {ActivatedRoute, Router} from "@angular/router";
import { isPlatformBrowser, Location } from '@angular/common';
import { Inject, PLATFORM_ID } from '@angular/core';

@Component({
    selector: 'app-lang-add',
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './translations-add.component.html',
    styleUrl: './translations-add.component.scss'
})
export class TranslationsAddComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  message: string = '';
  fb = inject(FormBuilder)
  languages = Object.keys(LanguagesEnum)
  translationService = inject(TranslationService)
  router = inject(Router)
  route = inject(ActivatedRoute)
  langAddForm = this.fb.group({
    code: ['', Validators.required],
    translations: this.fb.array([])
  })
  id = this.route.snapshot.params['id']

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private location: Location
  ) {}

  ngOnInit() {

    if(this.id) {
      this.translationService.getOne(this.id).subscribe((res: any) => {
        this.langAddForm.patchValue({
          code: res.code,
          translations: res.translations
        })
      })
    }

    this.languages.forEach(e => {
      this.translations.push(
        this.fb.group({
          lang: e,
          text: ['']
        })
      )
    })
  }

  get translations() {
    return this.langAddForm.get('translations') as FormArray;
  }

  onSubmit() {
    if (this.id) {
      this.translationService.update(this.id, this.langAddForm).subscribe(() => {
        this.openModal('Перевод успешно обновлен.');
      });
      return;
    }

    this.translationService.addTranslation(this.langAddForm).subscribe(() => {
      this.openModal('Перевод успешно добавлен.');
    });
  }

  openModal(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal();
  }

  closeModal(dialog: HTMLDialogElement) {
    dialog.close();
    this.router.navigate(['/translations']);
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (window.history.length > 1) {
        this.location.back();
      } else {
        this.router.navigate(['/']);
      }
    } else {
      console.log('Back button not available in SSR.');
    }
  }
}
