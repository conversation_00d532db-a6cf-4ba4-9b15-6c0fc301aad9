<div class="panel">
  <button class="p-2 ml-auto block" (click)="goBack()">X</button>
  <dialog #dialog class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(dialog)" class="btn btn-primary">OK</button>
      </div>
    </div>
  </dialog>
  <form class="space-y-5" [formGroup]="langAddForm" (submit)="onSubmit()">
    <div>
      <label>Символьный код (латиница)</label>
      <div [ngClass]="{ 'has-error': langAddForm.get('code')?.invalid && langAddForm.get('code')?.touched }" class="flex items-center">
        <input required type="text" class="form-input" placeholder="Символьный код (латиница)" formControlName="code"><span class="asterix">*</span>
        <span title="Символьный код (латиница)" class="cursor-pointer mx-2">
          &#9432;
        </span>
      </div>
    </div>
    <div formArrayName="translations" class="space-y-5">
        <div *ngFor="let translation of translations.controls; let i = index" [formGroupName]="i">
          <label>{{translation.get('lang')!.value.toUpperCase()}}</label>
          <div class="flex items-center">
            <input formControlName="text" class="form-input" />
            <span [title]="translation.get('lang')!.value.toUpperCase()" class="cursor-pointer mx-2">
              &#9432;
            </span>
          </div>
        </div>
    </div>
    <button [disabled]="langAddForm.invalid" type="submit" class="btn btn-success">Сохранить</button>
  </form>
</div>
