// TranslationsComponent specific styles
:host {
  display: block;
}

// Table styling for translations
.table-responsive {
  overflow-x: auto;

  table {
    min-width: 100%;
    border-collapse: collapse;

    th, td {
      border: none;
    }

    tbody tr {
      transition: background-color 0.2s ease;
    }

    // Code column styling - removed background since it's handled globally
    td:first-child {
      font-family: 'Courier New', monospace;
      font-size: 0.875rem;
    }

    // Translation text columns
    td:not(:first-child):not(:last-child) {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// Action buttons styling
.admin-table-actions {
  .btn {
    min-width: auto;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
}

// Modal content styling
.admin-modal-content {
  color: #374151;

  .dark & {
    color: #d1d5db;
  }
}