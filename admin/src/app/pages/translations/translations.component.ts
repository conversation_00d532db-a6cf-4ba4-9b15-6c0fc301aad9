import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {TranslationService} from "@/services/translation.service";

@Component({
    selector: 'app-lang',
    imports: [RouterLink],
    templateUrl: './translations.component.html',
    styleUrl: './translations.component.scss'
})
export class TranslationsComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  message: string = '';
  translationService = inject(TranslationService)
  router = inject(Router)
  ngOnInit() {
    this.translationService.getTranslations().subscribe()
  }

  delete(code: string) { 
    this.translationService.delete(code).subscribe(() => {
      this.openModal('Перевод успешно удален.');
    });
  }

  openModal(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal();
  }

  closeModal(dialog: HTMLDialogElement) {
    dialog.close();
  }
}
