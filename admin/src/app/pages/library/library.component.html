<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Библиотека</h1>
    </div>
    <div class="admin-actions">
      <!-- <button class="btn btn-primary">
        Импорт
        <input type="file" (change)="import($event)" class="hidden">
      </button> -->
    </div>
  </div>

  <!-- Modals -->
  <dialog #modal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(modal)" class="btn btn-primary">Да</button>
      </div>
    </div>
  </dialog>

  <dialog #confirmModal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="confirmDelete(true, confirmModal)" class="btn btn-danger">Да</button>
        <button (click)="confirmDelete(false, confirmModal)" class="btn btn-outline-secondary">Отмена</button>
      </div>
    </div>
  </dialog>

  <!-- Content -->
  <div class="admin-content-wrapper admin-table">
    <p-table
  [columns]="selectedColumns"
  [value]="libraryService.list()"
  dataKey="id"
  [scrollable]="true"
  [resizableColumns]="true"
  [reorderableColumns]="true"
  columnResizeMode="expand"
  styleClass="p-datatable-gridlines"
  scrollHeight="800px"
  [tableStyle]="{'min-width': '10rem'}">
<ng-template #header let-columns>
  <tr>
    @for(col of columns; track col.id) {
      <th pResizableColumn pReorderableColumn [pSortableColumn]="col.field">
        {{col.header}} <p-sortIcon [field]="col.field" />
      </th>
    }

  </tr>
</ng-template>
<ng-template #body let-book let-columns="columns">
  <tr >
    @for(col of columns; track col.id) {
      <td>
        @switch (col.field) {
          @case ('ru') {
            {{getTitle('ru', book.translations)}}
          }
          @case ('en') {
            {{getTitle('en', book.translations)}}
          }
          @case ('de') {
            {{getTitle('de', book.translations)}}
          }
          @case ('ua') {
            {{getTitle('ua', book.translations)}}
          }
          @case ('it') {
            {{getTitle('it', book.translations)}}
          }
          @default {
            {{book.translations[1][col.field]}}
          }
        }
      </td>
    }
  </tr>
</ng-template>
  <ng-template #emptymessage>
      <tr>
          <td colspan="6">Нет данных</td>
      </tr>
    </ng-template>
  </p-table>
  </div>
</div>
