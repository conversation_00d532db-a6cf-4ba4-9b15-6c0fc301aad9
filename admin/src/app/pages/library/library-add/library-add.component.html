<div class="panel">
  <button class="p-2 ml-auto block" (click)="goBack()">X</button>
  <form [formGroup]="forms[selectedLanguage]" class="space-y-5">
    <div class="mb-5">
      <div class="mt-3 flex flex-wrap border-b border-white-light dark:border-[#191e3a]">
        @for(tab of tabs; track $index) {
          <a
            href="javascript:"
            class="-mb-[1px] block border border-transparent p-3.5 py-2 !outline-none transition duration-300 hover:text-primary dark:hover:border-b-black"
            [ngClass]="{ '!border-white-light !border-b-white  text-primary dark:!border-[#191e3a] dark:!border-b-black': selectedTab === $index }"
            (click)="selectedTab = $index"
          >
            {{tab}}
          </a>
        }
      </div>
    </div>

    <div class="tab-content" [ngClass]="{'tab-active': selectedTab == 0, 'space-y-5': true}">
      @if(this.id) {
        <div>
          <button class="btn btn-primary" (click)="open()">Перейти</button>
        </div>
      }
      <div>
        <label>Язык</label>
        <select class="form-select" (change)="changeFormLanguage($event)">
          @for(lang of languages; track $index) {
            <option [value]="lang">{{lang}}</option>
          }
        </select>
      </div>
      <div>
        <label>Название</label>
        <input type="text" formControlName="title" class="form-input" (input)="updateCode($event)">
      </div>
      <div>
        <label>Автор</label>
        <input type="text" formControlName="author" class="form-input">
      </div>
      <div>
        <label>Чтец</label>
        <input type="text" formControlName="reader" class="form-input">
      </div>
      <div>
        <label>Аудио (ссылка)</label>
        <input type="text" formControlName="audio" class="form-input">
      </div>
      <div>
        <label>Кол-во страниц</label>
        <input type="text" formControlName="pages" class="form-input">
      </div>
      <div>
        <label>Краткое содержание</label>
        <input type="text" formControlName="summary" class="form-input">
      </div>
      <div>
        <label>Ключевые слова</label>
        <input type="text" formControlName="tags" class="form-input">
      </div>
      <div>
        <label>Обложка</label>
        <input accept="image/*" type="file"   accept="image/*" class="form-input" (change)="uploadFile($event, 'cover')">
      </div>
      <div>
        <label>Файл (epub)</label>
        <input accept=".epub" type="file"  class="form-input" (change)="uploadFile($event, 'file')">
      </div>
    </div>

    <div class="tab-content" [ngClass]="{'tab-active': selectedTab == 1, 'space-y-5': true}">
      <div>
        <label>Символьный код</label>
        <input type="text" formControlName="code" class="form-input">
      </div>
      <div>
        <label>SEO-заголовок</label>
        <input type="text" formControlName="seo_title" class="form-input">
      </div>
      <div>
        <label>SEO-описание</label>
        <input type="text" formControlName="seo_description" class="form-input">
      </div>
    </div>






    <div>
      <button class="btn btn-success" (click)="onSubmit()">Сохранить</button>
    </div>
  </form>
</div>
