import {Component, inject, signal} from '@angular/core';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import { Inject, PLATFORM_ID } from '@angular/core';
import {LibraryService} from "@/services/library.service";
import {ActivatedRoute, Router} from "@angular/router";
import {CommonModule} from "@angular/common";
import {environment} from "../../../../environments/environment";
import slugify from "slugify";
import {LanguagesEnum} from "@/enums/languages.enum";
import {FileService} from "@/services/file.service";
import { isPlatformBrowser, Location } from '@angular/common';

@Component({
    selector: 'app-library-add',
    imports: [
        CommonModule,
        ReactiveFormsModule,
    ],
    templateUrl: './library-add.component.html',
    styleUrl: './library-add.component.scss'
})
export class LibraryAddComponent {
  route = inject(ActivatedRoute)
  router = inject(Router)
  fb = inject(FormBuilder)
  libraryService = inject(LibraryService)
  fileService = inject(FileService)
  fields = {
    id: [null],
    title: [null, Validators.required],
    author: [null],
    reader: [null],
    audio: [null],
    seo_title: [null],
    seo_description: [null],
    code: [null],
    summary: [null],
    tags: [null],
    pages: [null]
  }
  forms: any = {}
  data: any = signal(null)
  id = this.route.snapshot.params['id'];
  serverUrl = environment.serverUrl;
  tabs = ['Основная информация', 'SEO']
  selectedTab = 0
  languages = Object.values(LanguagesEnum)
  selectedLanguage = 'ru'

  files: {[key: string]: {[key: string]: {id: number, name: string}} | null} = {}

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private location: Location
  ) {}

  ngOnInit() {
    for(let lang of this.languages) {
      this.forms[lang] = this.fb.group(this.fields)
    }

    console.log(this.id)

    if(this.id) {
      this.libraryService.get(this.id).subscribe((res: any) => {
        let code = null
        for(let item of res.translations) {
          if(item.lang == 'ru' && item.code) code = item.code
          this.forms[item.lang].patchValue(item)
        }
        for(let i in this.forms) {
          if(!this.forms[i].value.code) this.forms[i].patchValue({code})
        }
      })
    }
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (window.history.length > 1) {
        this.location.back();
      } else {
        this.router.navigate(['/']);
      }
    } else {
      console.log('Back button not available in SSR.');
    }
  }

  changeFormLanguage(e: Event) {
    this.selectedLanguage = (e.target as HTMLSelectElement).value
  }

  updateCode(e: Event) {
    if(this.selectedLanguage !== 'ru') return
    const title = (e.target as HTMLInputElement).value
    Object.values(LanguagesEnum).map(k => {
      this.forms[k].patchValue({
        code: slugify(title, {lower: true})
      })
    })
  }

  onSubmit() {
    const result: {code: string, translations: {lang: string, fields: FormGroup}[]} = {
      code: this.forms['ru'].value.code,
      translations: []
    }

    for(let lang in this.forms) {
      if(!this.forms[lang].value.title) continue;
      result.translations.push({
        lang,
        ...this.forms[lang].value,
        ...this.files[lang]
      })
    }

    if(this.id) return this.libraryService.update(this.id, result).subscribe()
    return this.libraryService.create(result).subscribe((res: any) => this.router.navigate(['/library/' + res.id]))
  }

  onFileChange(e: Event) {
    const files = (e.target as HTMLInputElement).files
    if(!files) return
    //this.file = files[0]
  }

  uploadFile(e: Event, name: string) {
    const files: FileList = (e.target as HTMLInputElement).files!
    this.fileService.upload(files, 'library').subscribe(
      (res: any) => {
        this.files[this.selectedLanguage] = {
          [name]: res[0],
          ...this.files[this.selectedLanguage]
        }
      }
    )
  }

  open() {
    window.open(this.environment.clientUrl + this.selectedLanguage + '/library/' + this.forms[this.selectedLanguage].value.code)
  }

  protected readonly environment = environment;
}
