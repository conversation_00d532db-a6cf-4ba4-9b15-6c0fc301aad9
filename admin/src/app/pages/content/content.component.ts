import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {ContentService} from "@/services/content.service";
import {DatePipe} from "@angular/common";
import {IconModule} from "@/shared/icon/icon.module";
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'app-content',
    imports: [
        RouterLink,
        IconModule,
        TableModule,
        ButtonModule,
        DatePipe
    ],
    templateUrl: './content.component.html',
    styleUrl: './content.component.scss'
})
export class ContentComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  message: string = '';
  contentService = inject(ContentService)
  router = inject(Router)
  tree = []
  show: any = JSON.parse(localStorage.getItem('show') || '[]')
  query: string | null = null
  showOnlyActive: boolean = false;

  ngOnInit() {
    this.contentService.getTree()
  }


  get filtered() {
    let result = this.contentService.tree();

    if (this.query) {
      result = result.map((e: any) => {
        const contents = e.contents.filter((k: any) => {
          const titleMatch = k.title.toLowerCase().indexOf(this.query!.toLowerCase()) > -1;
          const authorMatch = k.author?.toLowerCase().indexOf(this.query!.toLowerCase()) > -1;
          return titleMatch || authorMatch;
        })
        return contents.length ? {...e, contents} : false
      }).filter((e: any) => e);
    }

    if (this.showOnlyActive) {
      result = result.map((e: any) => {
        const contents = e.contents.filter((k: any) => k.active === false);
        return contents.length ? {...e, contents} : false
      }).filter((e: any) => e);
    }

    return result;
  }

  // Toggle active content filter
  toggleActiveFilter() {
    this.showOnlyActive = !this.showOnlyActive;
  }

  toggleTree(id: number) {
    if(this.show.includes(id)) {
      this.show.splice(this.show.findIndex((e: number) => e == id), 1)
    } else this.show.push(id)
    localStorage.setItem('show', JSON.stringify(this.show))
  }

 search(e: Event) {
    this.query = (e.target as HTMLInputElement).value;

    // If there's a search query, expand all tabs to show results
    if (this.query && this.query.trim() !== '') {
      const allCategoryIds = this.contentService.tree().map((category: any) => category.id);
      this.show = [...allCategoryIds];
      // Update localStorage
      localStorage.setItem('show', JSON.stringify(this.show));
      // Update expanded state flag
      this.isExpanded = true;
    }
  }

// Проверяет, раскрыта ли строка
isRowExpanded(categoryId: number): boolean {
  return this.show.includes(categoryId);
}

// Обрабатывает клик по иконке для переключения состояния строки
toggleRow(category: any, event: Event) {
  event.stopPropagation(); // Предотвращает всплытие события

  if (this.show.includes(category.id)) {
    // Убрать из массива раскрытых
    this.show = this.show.filter((id: any) => id !== category.id);
  } else {
    // Добавить в массив раскрытых
    this.show.push(category.id);
  }

  // Сохранить состояние в localStorage
  localStorage.setItem('show', JSON.stringify(this.show));
}

  deleteCategory(categoryId: number) {
    this.openConfirmationDialog('Удалить категорию?').then((confirmed) => {
      if (confirmed) {
        this.contentService.deleteCategory(categoryId).subscribe({
          next: () => this.openDialog('Удалена категория!'),
          error: () => this.openDialog('Ошибка удаления категории, попробуйте еще раз')
        });
      }
    });
  }

  openDialog(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal()
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeDialog();
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  closeDialog() {
    this.confirmDialog.nativeElement.close();
  }

  editCategory(id: number) {
    this.router.navigate(['/category/' + id]);
  }

  isExpanded: boolean = false;

  toggleAll() {
    this.isExpanded = !this.isExpanded;

    if (this.isExpanded) {
      const allCategoryIds = this.contentService.tree().map((category: any) => category.id);
      this.show = [...allCategoryIds];
    } else {
      this.show = [];
    }

    localStorage.setItem('show', JSON.stringify(this.show));
  }
  getExpandedRowKeys() {
    const expandedRowKeys: Record<string, boolean> = {};
    this.show.forEach((id: number) => {
      expandedRowKeys[id] = true;
    });
    return expandedRowKeys;
  }

  moveCategory(direction: 'up' | 'down', index: number) {
    const categories = [...this.contentService.tree()];

    const itemToMove = categories[index];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    if (newIndex < 0 || newIndex >= categories.length) {
      return;
    }

    categories.splice(index, 1);
    categories.splice(newIndex, 0, itemToMove);

    this.contentService.tree.set(categories);

    const orderPayload = categories.map((cat, order) => ({
      id: cat.id,
      order
    }));

    this.contentService.updateCategoryOrder(orderPayload).subscribe();
  }

}
