import {SimpleUploadAdapter} from "ckeditor5";
import {environment} from "../../../../environments/environment";

export class CustomUploadAdapter {
  public loader: any;
  public url: string;

  constructor(loader: any, url: string) {
    this.loader = loader;
    this.url = url
  }

  upload() {
    return new Promise(async (resolve, reject) => {
      this.loader.file.then(async (file: any) => {
        const formData = new FormData();
        formData.append('file[]', file);
        formData.append('name[]', encodeURIComponent(file.name));

        const response = await fetch(environment.apiUrl + '/file/upload/tmp', {
          method: 'POST',
          body: formData,
        });
        const result = await response.json();
        resolve({
          default: environment.serverUrl + '/upload/' + result[0].name
        });
      });
    });
  }

  abort() {

  }
}
