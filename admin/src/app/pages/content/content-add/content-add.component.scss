@import 'ckeditor5/ckeditor5.css';
@import "@ng-select/ng-select/themes/default.theme.css";

@media print {
  body {
    margin: 0 !important;
  }
}

.ck-content {
  line-height: 1.6;
  word-break: break-word;
}

.editor-container_classic-editor .editor-container__editor {
  min-width: 795px;
  max-width: 795px;
}
:host ::ng-deep .ck-editor__editable_inline {
  min-height: 200px !important;
}

.ck-editor__editable {
  min-height: 150px !important;
}

/* Fixed buttons styles */
.fixed-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  gap: 3px;
  justify-content: center;
}

/* Add padding to the bottom of the panel to prevent content from being hidden behind fixed buttons */
.panel {
  padding-bottom: 70px;
}

/* Dark mode support */
:host-context(.dark) .fixed-buttons {
  background-color: #1e2e4a;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

/* Full-screen editor styles */
.editor-container {
  position: relative;
}

.fullscreen-toggle {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1001;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.fullscreen-toggle:hover {
  background-color: #e9ecef;
}

.editor-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background-color: #fff;
  padding: 20px;
  overflow-y: auto;
}

.editor-fullscreen ::ng-deep .ck-editor__editable_inline {
  min-height: calc(100vh - 130px) !important;
  max-height: none !important;
}

.editor-fullscreen .fullscreen-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
}

:host-context(.dark) .fullscreen-toggle {
  background-color: #1e2e4a;
  border-color: #2d3a4f;
  color: #fff;
}

:host-context(.dark) .fullscreen-toggle:hover {
  background-color: #2d3a4f;
}

:host-context(.dark) .editor-fullscreen {
  background-color: #1e2e4a;
}

a.has-errors {
  color: #f44336;
  border-bottom: 2px solid #f44336 !important;
}
