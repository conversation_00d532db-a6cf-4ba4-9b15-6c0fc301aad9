@import "primeicons/primeicons.css";
:host ::ng-deep .p-datatable {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
  
  :host ::ng-deep .p-datatable .p-datatable-header {
    background: white;
    border: none;
    padding: 1rem;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    background: #f9fafb !important;
    border-bottom: 2px solid #e5e7eb !important;
    color: #111827 !important;
    padding: 1rem;
    font-weight: 600 !important;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr {
    background: white;
    border-bottom: 1px solid #e5e7eb;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
    background: #f9fafb;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    border: none;
  }
  
  :host ::ng-deep .p-button.p-button-text {
    color: #6b7280;
  }
  
  :host ::ng-deep .p-datatable .p-sortable-column:hover {
    background: #f9fafb;
  }

  :host ::ng-deep .p-datatable {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
  
  :host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    background: #f9fafb !important;
    border-bottom: 2px solid #e5e7eb !important;
    color: #111827 !important;
    padding: 1rem;
    font-weight: 600 !important;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    border: none;
    color: #000000;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
    background: #f9fafb;
  }
  
  :host ::ng-deep .p-sortable-column-icon {
    color: #000000;
  }
  
  :host ::ng-deep .p-rowgroup-footer td {
    font-weight: 700;
}

:host ::ng-deep .p-rowgroup-header {
    span {
        font-weight: 700;
    }

    .p-row-toggler {
        vertical-align: middle;
        margin-right: .25rem;
    }
}