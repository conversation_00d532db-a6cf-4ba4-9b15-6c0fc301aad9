<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Конструктор</h1>
    </div>
    <div class="admin-actions">
      <button (click)="carousel.showCarouselForm(true)" class="btn btn-primary">Создать карусель</button>
    </div>
  </div>

  <!-- Modals -->
  <dialog #modal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(modal)" class="btn btn-primary">Да</button>
      </div>
    </div>
  </dialog>

  <dialog #confirmDialog class="admin-modal">
    <div class="admin-modal-content">
      <div>{{ message }}</div>
      <div class="admin-modal-footer">
        <button class="btn btn-danger">Да</button>
        <button class="btn btn-outline-secondary">Отмена</button>
      </div>
    </div>
  </dialog>

  <!-- Content -->
  <div class="admin-content-wrapper">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Название</th>
            <th>Тип</th>
            <th>Размер</th>
            <th>Элементов</th>
            <th class="text-center w-64">Действия</th>
          </tr>
        </thead>
        <tbody>
          @for(item of carousels.value; track item.id) {
            <tr>
              <td>{{item.id}}</td>
              <td>{{item.title}}</td>
              <td>{{item.type}}</td>
              <td>{{item.size}}</td>
              <td>{{item.items.length}}</td>
              <td>
                <div class="admin-table-actions">
                  <button (click)="edit($index, item)" class="btn btn-sm btn-primary">Редактировать</button>
                  <button (click)="delete(item.id)" class="btn btn-sm btn-danger">Удалить</button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
    <carousel (onItemAdded)="onCarouselAdded($event)" (onItemEdited)="onCarouselEdited($event)"/>
  </div>
</div>
