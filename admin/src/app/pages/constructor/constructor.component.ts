import { Component, ElementRef, inject, ViewChild } from '@angular/core';
import { ConstructorService } from "@/services/constructor.service";
import {CarouselComponent} from "@/components/carousel/carousel.component";
import {FormArray, FormBuilder, FormGroup} from "@angular/forms";

@Component({
    selector: 'app-constructor',
    imports: [CarouselComponent],
    templateUrl: './constructor.component.html',
    styleUrl: './constructor.component.scss'
})
export class ConstructorComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild(CarouselComponent) carousel!: CarouselComponent;
  fb = inject(FormBuilder)
  form = this.fb.group({
    carousel: this.fb.array([])
  })
  deletingItemId: number | null = null;
  message: string = "";
  constructorService = inject(ConstructorService)

  ngOnInit() {
    this.getAll()
  }

  getAll() {
    this.carousels.clear()
    this.constructorService.getAll().subscribe(res => {
      for(let item of res) {
        this.carousels.push(this.fb.group({
          ...item,
          items: this.fb.array(item.items.map((item: any) => this.fb.group(item)))
        }));
      }
    })
  }

  get carousels() {
    return this.form.get("carousel") as FormArray
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  delete(id: number) {
    this.deletingItemId = id;
    this.openConfirmationDialog('Удалить лендинг?').then((confirmed) => {
      if (confirmed) {
        this.constructorService.deleteBlock(id).subscribe({
          next: () => {
            this.openModal('Удален лендинг!')
            this.getAll()
          },
          error: () => this.openModal('Ошибка удаления, попробуйте еще раз')
        });
      }
    });
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  edit(i: number, item: any) {
    item.index = i;

    const itemsArray: FormArray = this.fb.array([]);
    for (let itemElement of item.items) {
      itemsArray.push(this.fb.group(itemElement));
    }

    this.carousel.carouselForm.patchValue({...item, items: []});
    this.carousel.carouselForm.setControl('items', itemsArray);
    this.carousel.showCarouselForm();
  }

  onCarouselAdded(form: FormGroup) {
    this.constructorService.create(form.value).subscribe(() => this.getAll())
  }

  onCarouselEdited({index, form}: {index: number, form: FormGroup}) {
    this.constructorService.create({id: this.carousels.at(index).value.id, ...form.value}).subscribe(() => this.getAll())
  }
}
