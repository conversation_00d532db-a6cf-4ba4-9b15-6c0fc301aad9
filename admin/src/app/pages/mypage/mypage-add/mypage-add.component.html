<div class="panel">
  <dialog #modal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(modal)" class="btn btn-primary">Да</button>
      </div>
    </div>
  </dialog>
  <div class="mb-5">
    <div class="mt-3 flex flex-wrap border-b border-white-light dark:border-[#191e3a]">
      @for(language of languages; track $index) {
        <a
          href="javascript:"
          class="-mb-[1px] block border border-transparent p-3.5 py-2 !outline-none transition duration-300 hover:text-primary dark:hover:border-b-black"
          [ngClass]="{ '!border-white-light !border-b-white  text-primary dark:!border-[#191e3a] dark:!border-b-black': selectedLanguage === language }"
          (click)="selectLanguage(language)"
        >
          {{language.toUpperCase()}}
        </a>
      }
    </div>
  </div>

  @if(id && id !== 'add') {
    <div class="flex gap-2 mb-5">
      <button class="btn btn-sm" (click)="copyLink()">Скопировать ссылку</button>
      <button class="btn btn-sm" (click)="followLink()">Перейти</button>
    </div>
  }

  @for(item of forms; track item.lang) {
    <form [formGroup]="item.form" class="space-y-3" *ngIf="selectedLanguage == item.lang">
      <div>
        <label>Заголовок</label>
        <div [ngClass]="{ 'has-error': item.form.get('title')?.invalid && item.form.get('title')?.touched }" class="flex items-center">
          <input formControlName="title" type="text" class="form-input"><span class="asterix">*</span>
          <span title="Заголовок" class="cursor-pointer mx-2">
          &#9432;
        </span>
        </div>
      </div>

      <div>
        <label>Описание</label>
        <textarea formControlName="description" class="form-textarea"></textarea>
      </div>

      <div>
        <label>Фон первого блока</label>
        <div [ngClass]="{ 'has-error': item.form.get('bg')?.invalid && item.form.get('bg')?.touched }" class="flex items-center">
          <input required type="file" accept="image/*" class="form-input" (change)="uploadBackground($event)"><span class="asterix">*</span>
          <span title="Фон первого блока" class="cursor-pointer mx-2">
          &#9432;
        </span>
        </div>


        @if(item.form.value?.bg) {
          <PhotoPreview [disableRemoveBtn]="true" (onItemRemoved)="onBackgroundRemoved()" [items]="[item.form.value['bg']]"/>
        }
      </div>

      <div>
        <label>SEO-заголовок</label>
        <input type="text" class="form-input" formControlName="seo_title">
      </div>

      <div>
        <label>SEO-описание</label>
        <input type="text" class="form-input" formControlName="seo_description">
      </div>

      <div>
        <label>Кнопки</label>
        <button class="btn btn-primary btn-sm" (click)="showAddButtonForm()">Добавить</button>
        <table>
          <tbody>
          @for(button of buttons.value; track button.name; let i = $index) {
            <tr>
              <td>{{button.name}}</td>
              <td>
                <div class="flex gap-2">
                  <button class="btn btn-primary btn-sm" (click)="editButton(i, button)">Редактировать</button>
                  <button class="btn btn-danger btn-sm" (click)="removeButton(i)">Удалить</button>
                </div>
              </td>
            </tr>
          }
          </tbody>
        </table>
      </div>

      <div>
        <label>Карусель</label>
        <button class="btn btn-primary btn-sm" (click)="carousel.showCarouselForm(true)">Добавить</button>
        <table class="mt-5 mb-5">
          <thead>
          <tr>
            <th>Название</th>
            <th></th>
          </tr>
          </thead>
          <tbody>
          @for(carouselItem of carouselItems.value; track $index; let i = $index) {
            <tr>
              <td>{{carouselItem.title}}</td>
              <td>
                <div class="d-flex gap-2">
                  <button class="btn btn-primary btn-sm" (click)="editCarousel(i, carouselItem)">Редактировать</button>
                  <button class="btn btn-danger btn-sm" (click)="removeCarousel(i, item.form)">Удалить</button>
                </div>
              </td>
            </tr>
          }
          </tbody>
        </table>
      </div>

      <div #editorElement>
        <ckeditor formControlName="content" [editor]="Editor" [config]="editorConfig"  (ready)="onEditorReady($event)" />
      </div>

      <div>
        <button [disabled]="item.form.invalid" type="submit" class="btn btn-success" (click)="onSubmit()">Сохранить</button>
      </div>
    </form>
  }


  <dialog #addButtonDialog class="admin-modal">
    <div class="admin-modal-content">
      <form [formGroup]="buttonForm" class="space-y-3" style="min-width: 400px">
        <div>
          <label>Название</label>
          <input formControlName="name" type="text" class="form-input">
        </div>
        <div>
          <label>Ссылка</label>
          <input formControlName="link" type="text" class="form-input">
        </div>
        <div class="admin-modal-footer">
          <button class="btn btn-primary" (click)="addButton()">Сохранить</button>
          <button class="btn btn-outline-secondary" (click)="closeAddButtonForm()">Закрыть</button>
        </div>
      </form>
    </div>
  </dialog>


  <carousel folder="personalPage" (onItemAdded)="onCarouselAdded($event)" (onItemEdited)="onCarouselEdited($event)"/>

</div>
