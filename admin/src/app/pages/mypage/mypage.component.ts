import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {MypageService} from "@/services/mypage.service";

@Component({
    selector: 'app-mypage',
    imports: [
        RouterLink
    ],
    templateUrl: './mypage.component.html',
    styleUrl: './mypage.component.scss'
})
export class MypageComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  message: string = "";
  mypageService = inject(MypageService);
  router = inject(Router);
  items: any = []

  ngOnInit(){
    this.getAll()
  }

  getAll() {
    this.mypageService.getAll().subscribe((res: any) => this.items = res)
  }
  
  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  removePage(id: number) {
    this.openConfirmationDialog('Удалить страницу?').then((confirmed) => {
      if (confirmed) {
        this.mypageService.removePage(id).subscribe({
          next: () => {
            this.openModal('Удалена страница!')
            this.getAll()
          },
          error: () => {
            this.openModal('Ошибка удаления, попробуйте еще раз')
            this.getAll()
          }
        });
      }
    });
  }
}
