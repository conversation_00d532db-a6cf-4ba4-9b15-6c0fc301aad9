<div class="flex gap-2 mb-3">
	<button
		type='button'
		class="btn btn-primary"
		[disabled]="isCreating"
		(click)="create()">
		@if (isCreating) {
			<span class="spinner-border spinner-border-sm me-1" role="status"></span>
		}
		Создать резервную копию
	</button>

	<button
		type='button'
		class="btn btn-success"
		[disabled]="isImporting"
		(click)="triggerFileInput()">
		@if (isImporting) {
			<span class="spinner-border spinner-border-sm me-1" role="status"></span>
		}
		Импортировать резервную копию
	</button>

	<input
		type="file"
		id="backup-file-input"
		accept=".tar"
		style="display: none;"
		(change)="onFileSelected($event)">
</div>
<p-table [columns]="selectedColumns" [value]="items" dataKey="id">
  <ng-template #header let-columns>
    <tr>
      @for(col of columns; track col.id) {
        <th pResizableColumn pReorderableColumn [pSortableColumn]="col.field">
          {{col.header}} <p-sortIcon [field]="col.field" />
        </th>
      }
			<th>Действия</th>
    </tr>
  </ng-template>

  <ng-template #body let-item let-columns="columns">
    <tr>
          @for(col of columns; track col.id) {
            @switch (col.field) {
              @case('createdAt') {
                <td>
                  {{moment(item[col.field]).format('DD.MM.YYYY HH:mm:ss')}}
                </td>
              }
              @case('fileSize') {
                <td>
                  {{Math.round(item[col.field] / 1024 / 1024 * 100) / 100}} Mb 
                </td>
              }
							@case('status') {
								<td>
									@if (item[col.field] == 'in_progress') {
										<span class="badge bg-warning">Выполняется</span>
									}
									@if (item[col.field] == 'completed') {
										<span class="badge bg-success">Завершено</span>
									}
									@if (item[col.field] == 'failed') {
										<span class="badge bg-danger">Ошибка</span>
									}
									@if (item[col.field] == 'pending') {
										<span class="badge bg-primary">Ожидание</span>
									}
								</td>
							}
							@default {
								<td>
									{{item[col.field]}}
								</td>
							}
          }}
						<td>
							<div class='flex gap-2'>
							<button
								*ngIf='item.status == "completed"'
								class="btn btn-sm btn-primary"
								[disabled]="isDownloading(item.id)"
								(click)="download(item)">
								@if (isDownloading(item.id)) {
									<span class="spinner-border spinner-border-sm me-1" role="status"></span>
								}
								Скачать
							</button>
							<button
								*ngIf='item.status == "completed"'
								class="btn btn-sm btn-success"
								[disabled]="isRestoring(item.id)"
								(click)="restore(item.id)">
								@if (isRestoring(item.id)) {
									<span class="spinner-border spinner-border-sm me-1" role="status"></span>
								}
								Восстановить
							</button>
							<button
								class="btn btn-sm btn-danger"
								[disabled]="isDeleting(item.id)"
								(click)="remove(item.id)">
								@if (isDeleting(item.id)) {
									<span class="spinner-border spinner-border-sm me-1" role="status"></span>
								}
								Удалить
							</button>
							</div>
						</td>
    </tr>
  </ng-template>
</p-table>

<admin-dialog></admin-dialog>