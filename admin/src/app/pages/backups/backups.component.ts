import { AdminDialogComponent } from '@/components/admin-dialog/admin-dialog.component'
import { BackupsService } from '@/services/backups.service'
import { CommonModule } from '@angular/common'
import { Component, inject, ViewChild } from '@angular/core'
import moment from 'moment'
import { TableModule } from 'primeng/table'
import { environment } from '../../../environments/environment'

@Component({
  selector: 'app-backups',
  imports: [TableModule, CommonModule, AdminDialogComponent],
  templateUrl: './backups.component.html',
  styleUrl: './backups.component.scss'
})
export class BackupsComponent {
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;

  backupsService = inject(BackupsService)
  selectedColumns =  [
    { field: 'id', header: 'ID' },
    { field: 'description', header: 'Описание' },
    { field: 'createdAt', header: 'Дата' },
    { field: 'fileSize', header: 'Размер' },
    { field: 'status', header: 'Статус' },
  ];
  moment = moment
  Math = Math

  items: any[] = []
  downloadingItems = new Set<number>()
  restoringItems = new Set<number>()
  deletingItems = new Set<number>()
  isCreating = false
  isImporting = false

  ngOnInit() {
    this.get();
  }

  get() {
    this.backupsService.get().subscribe((res: any) => this.items = res)
  }

  create() {
    if (this.isCreating) return;

    this.isCreating = true;
    this.backupsService.create().subscribe({
      next: () => {
        this.get();
        this.isCreating = false;
      },
      error: (error) => {
        this.isCreating = false;
        this.adminDialog.showAlert('Ошибка создания резервной копии: ' + (error.error?.message || error.message || 'Неизвестная ошибка'));
      }
    });
  }

  remove(id: number) {
    if (this.deletingItems.has(id)) return;

    this.deletingItems.add(id);
    this.backupsService.delete(id).subscribe({
      next: () => {
        this.get();
        this.deletingItems.delete(id);
        this.adminDialog.showAlert('Резервная копия успешно удалена');
      },
      error: (error) => {
        this.deletingItems.delete(id);
        this.adminDialog.showAlert('Ошибка удаления резервной копии: ' + (error.error?.message || error.message || 'Неизвестная ошибка'));
      }
    });
  }

  download(item: any) {
    if (this.downloadingItems.has(item.id)) return;

    this.downloadingItems.add(item.id);

    const link = document.createElement('a');
    link.href = environment.serverUrl + `${environment.serverUrl.indexOf('localhost') > -1 ? '/' : ''}api/admin/backups/${item.id}/download`;
    link.download = `backup_${item.id}.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
      this.downloadingItems.delete(item.id);
    }, 2000);
  }

  restore(id: number) {
    if (this.restoringItems.has(id)) return;

    this.restoringItems.add(id);
    this.backupsService.restore(id).subscribe({
      next: () => {
        this.get();
        this.restoringItems.delete(id);
        this.adminDialog.showAlert('Резервная копия успешно восстановлена');
      },
      error: (error) => {
        this.restoringItems.delete(id);
        this.adminDialog.showAlert('Ошибка восстановления резервной копии: ' + (error.error?.message || error.message || 'Неизвестная ошибка'));
      }
    });
  }

  isDownloading(id: number): boolean {
    return this.downloadingItems.has(id);
  }

  isRestoring(id: number): boolean {
    return this.restoringItems.has(id);
  }

  isDeleting(id: number): boolean {
    return this.deletingItems.has(id);
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.importBackup(input.files[0]);
      input.value = '';
    }
  }

  importBackup(file: File) {
    if (this.isImporting) return;

    if (!file.name.endsWith('.tar')) {
      this.adminDialog.showAlert('Пожалуйста, выберите файл резервной копии (.tar)');
      return;
    }

    const maxSize = 500 * 1024 * 1024;
    if (file.size > maxSize) {
      this.adminDialog.showAlert('Размер файла не должен превышать 500MB');
      return;
    }

    this.isImporting = true;
    this.backupsService.import(file).subscribe({
      next: () => {
        this.get();
        this.isImporting = false;
        this.adminDialog.showAlert('Резервная копия успешно импортирована');
      },
      error: (error) => {
        this.isImporting = false;
        this.adminDialog.showAlert('Ошибка импорта резервной копии: ' + (error.error?.message || error.message || 'Неизвестная ошибка'));
      }
    });
  }

  triggerFileInput() {
    const fileInput = document.getElementById('backup-file-input') as HTMLInputElement;
    fileInput?.click();
  }
}
