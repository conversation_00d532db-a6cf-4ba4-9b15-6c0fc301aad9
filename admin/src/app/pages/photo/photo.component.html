<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Фотогалереи</h1>
    </div>
    <div class="admin-actions">
      <button [routerLink]="'/photo/add'" class="btn btn-primary">Добавить фотогалерею</button>
    </div>
  </div>

  <!-- Modals -->
  <dialog #modal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(modal)" class="btn btn-primary">Да</button>
      </div>
    </div>
  </dialog>

  <dialog #confirmDialog class="admin-modal">
    <div class="admin-modal-content">
      <div>{{ message }}</div>
      <div class="admin-modal-footer">
        <button class="btn btn-danger">Да</button>
        <button class="btn btn-outline-secondary">Отмена</button>
      </div>
    </div>
  </dialog>

  <!-- Content -->
  <div class="admin-content-wrapper">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Название папки</th>
            <th class="text-center w-80">Действия</th>
          </tr>
        </thead>
        <tbody>
        @for(item of photoService.photos; track $index) {
          <tr>
            <td>{{item.translations[0].title}}</td>
            <td>
              <div class="admin-table-actions">
                <button class="btn btn-sm btn-outline-primary" (click)="open(item)">Перейти</button>
                <button class="btn btn-sm btn-primary" (click)="router.navigate(['/photo/' + item.id])">Редактировать</button>
                <button class="btn btn-sm btn-danger" (click)="delete(item.id)">Удалить</button>
              </div>
            </td>
          </tr>
        }
        </tbody>
      </table>
    </div>
  </div>
</div>
