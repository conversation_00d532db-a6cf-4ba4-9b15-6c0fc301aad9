// PhotoComponent specific styles
:host {
  display: block;
}

// Ensure table styling consistency
.table-responsive {
  overflow-x: auto;

  table {
    min-width: 100%;
    border-collapse: collapse;

    th, td {
      border: none;
    }

    tbody tr {
      transition: background-color 0.2s ease;
    }
  }
}

// Action buttons styling
.admin-table-actions {
  .btn {
    min-width: auto;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
}

// Modal content styling
.admin-modal-content {
  color: #374151;

  .dark & {
    color: #d1d5db;
  }
}