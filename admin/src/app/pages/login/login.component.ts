import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component"
import { AuthService } from "@/services/auth.service"
import { Component, inject, ViewChild } from '@angular/core'
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms"
import { Router } from '@angular/router'

@Component({
    selector: 'app-root',
    imports: [ReactiveFormsModule, AdminDialogComponent],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss'
})
export class LoginComponent {
    @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
    router = inject(Router)
    authService = inject(AuthService);
    form = new FormGroup({
        email: new FormControl('', Validators.required),
        password: new FormControl('', Validators.required)
    })

    onSubmit() {
        this.authService.login(this.form.value).subscribe({
            next: () => this.router.navigate(['/']),
            error: () => {
                this.adminDialog.showAlert('Неверный логин или пароль')
            }
        })
        return false;
    }
}
