@import "primeicons/primeicons.css";

:host ::ng-deep {
    .p-multiselect-overlay {
        background-color: #fff;
        border: 1px solid #e5e7eb !important;
    }

    .p-multiselect-option {
        color: #000;
    }

    .p-multiselect-list-container {
                &::-webkit-scrollbar {
            width: 10px;
        }

        &::-webkit-scrollbar-track {
            background: #fff;
        }

        &::-webkit-scrollbar-thumb {
            background: #9f9f9f;
            border-radius: 5px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: #666;
        }
    }

    .p-multiselect-option:not(.p-multiselect-option-selected):not(.p-disabled).p-focus {
        background: #e5e7eb;
        color: #000;
    }

    .p-inputtext {
        background: #e5e7eb;
        color: #000;
    }

    .p-multiselect {
        background: transparent;
        border: 1px solid #e5e7eb !important;
    }

    .p-paginator {
        background: transparent;
        border: 1px solid #e5e7eb !important;
    }

    .p-paginator-page:not(.p-disabled):not(.p-paginator-page-selected):hover,
    .p-paginator-first:not(.p-disabled):hover,
    .p-paginator-prev:not(.p-disabled):hover,
    .p-paginator-next:not(.p-disabled):hover,
    .p-paginator-last:not(.p-disabled):hover {
        background: #aaa9a9;
    }

    .p-select {
        background: transparent;
        border: 1px solid #e5e7eb !important;
    }

    .p-select-overlay {
        background: #fff;
        border: 1px solid #e5e7eb !important;
    }

    .p-select-option.p-select-option-selected.p-focus {
        background: #aaa9a9;
    }

    .p-select-option {
        color: #000;
    }

    .p-select-option:not(.p-select-option-selected):not(.p-disabled).p-focus {
        background: #aaa9a9;
    }

    .p-paginator-page.p-paginator-page-selected {
        background: #aaa9a9;
    }

    .p-select-label {
        color: #000;
    }

    .p-multiselect-chip.p-chip {
        background-color: transparent !important;
        color: #000;
        border: 1px solid #e5e7eb !important;

        .p-chip-remove-icon {
            cursor: pointer;
            color: #000;
        }
    }

    .p-datatable-table-container {
        margin-top: 15px;

        &::-webkit-scrollbar {
            width: 10px;
        }

        &::-webkit-scrollbar-track {
            background: #fff;
        }

        &::-webkit-scrollbar-thumb {
            background: #9f9f9f;
            border-radius: 5px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: #666;
        }
    }
}