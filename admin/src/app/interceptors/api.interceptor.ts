import { HttpInterceptorFn } from '@angular/common/http';
import { environment } from '../../environments/environment';

export const apiInterceptor: HttpInterceptorFn = (req, next) => {
    // Клонируем запрос, добавляя базовый URL
    const apiReq = req.clone({
        url: `${environment.apiUrl}${req.url}`, // Добавляем базовый URL
    })
    // Перенаправляем изменённый запрос
    return next(apiReq);
};
