import {
  AccessibilityHelp,
  Autoformat,
  AutoLink,
  Autosave,
  BalloonToolbar,
  BlockQuote,
  Bold,
  Code,
  CodeBlock, Element,
  Essentials, FileRepository,
  FindAndReplace,
  FullPage,
  GeneralHtmlSupport,
  Heading,
  Highlight,
  HorizontalLine,
  HtmlComment,
  HtmlEmbed, Image, ImageCaption, ImageResize, ImageStyle, ImageToolbar, ImageUpload,
  Indent,
  IndentBlock,
  Italic,
  Link, LinkImage, Mention,
  Paragraph,
  SelectAll,
  ShowBlocks, SimpleUploadAdapter,
  SourceEditing,
  SpecialCharacters,
  SpecialCharactersArrows,
  SpecialCharactersCurrency,
  SpecialCharactersEssentials,
  SpecialCharactersLatin,
  SpecialCharactersMathematical,
  SpecialCharactersText,
  Strikethrough,
  TextPartLanguage,
  TextTransformation,
  Underline,
  Undo
} from "ckeditor5";
import {environment} from "../environments/environment";

export default {
  toolbar: {
    items: [
      'undo',
      'redo',
      '|',
      'sourceEditing',
      'showBlocks',
      '|',
      'heading',
      '|',
      'bold',
      'italic',
      'underline',
      '|',
      'highlight',
      'blockQuote',
      'codeBlock',
      '|',
      'outdent',
      'indent',

      'uploadImage',
      'imageStyle:inline',
      'imageStyle:block',
      'imageStyle:side',
      'toggleImageCaption', 'imageTextAlternative', 'ckboxImageEdit',
      'link',
      // 'linkImage',
      '|',
      'insertLink',
      'editLink',
      'unlink',
      // 'LinkImage'
    ],
    shouldNotGroupWhenFull: false
  },

  plugins: [
    AccessibilityHelp,
    Autoformat,
    AutoLink,
    Autosave,
    BalloonToolbar,
    BlockQuote,
    Bold,
    Code,
    CodeBlock,
    Essentials,
    FindAndReplace,
    FullPage,
    GeneralHtmlSupport,
    Heading,
    Highlight,
    HorizontalLine,
    HtmlComment,
    HtmlEmbed,
    Indent,
    IndentBlock,
    Italic,
    Link,
    Paragraph,
    SelectAll,
    ShowBlocks,
    SourceEditing,
    SpecialCharacters,
    SpecialCharactersArrows,
    SpecialCharactersCurrency,
    SpecialCharactersEssentials,
    SpecialCharactersLatin,
    SpecialCharactersMathematical,
    SpecialCharactersText,
    Strikethrough,
    TextPartLanguage,
    TextTransformation,
    Underline,
    Undo,
    Mention,
    Image,
    ImageToolbar,
    ImageCaption,
    ImageStyle,
    ImageResize,
    LinkImage,
    Image,
    ImageToolbar,
    ImageCaption,
    ImageStyle,
    ImageResize,
    ImageUpload,
    FileRepository,
    SimpleUploadAdapter
  ],
  balloonToolbar: ['bold', 'italic', '|', 'link'],
  image: {

    resizeOptions: [
      {
        name: 'resizeImage:original',
        value: null,
        icon: 'original'
      },
      {
        name: 'resizeImage:custom',
        value: 'custom',
        icon: 'custom'
      },
      {
        name: 'resizeImage:50',
        value: '50',
        icon: 'medium'
      },
      {
        name: 'resizeImage:75',
        value: '75',
        icon: 'large'
      }
    ],
    // toolbar: [
    //   'toggleImageCaption',
    //   'imageTextAlternative',
    //   'ckboxImageEdit',
    //   'resizeImage:50',
    //   'resizeImage:75',
    //   'resizeImage:original',
    //   'resizeImage:custom',
    //   'imageStyle:alignLeft',
    //   'imageStyle:alignCenter',
    //   'imageStyle:alignRight',
    //   '|',
    //   'imageResize'

    // ]
  },
  heading: {
    options: [
      {
        model: 'paragraph',
        title: 'Paragraph',
        class: 'ck-heading_paragraph'
      },
      {
        model: 'heading1',
        view: 'h1',
        title: 'Heading 1',
        class: 'ck-heading_heading1'
      },
      {
        model: 'heading2',
        view: 'h2',
        title: 'Heading 2',
        class: 'ck-heading_heading2'
      },
      {
        model: 'heading3',
        view: 'h3',
        title: 'Heading 3',
        class: 'ck-heading_heading3'
      },
      {
        model: 'heading4',
        view: 'h4',
        title: 'Heading 4',
        class: 'ck-heading_heading4'
      },
      {
        model: 'heading5',
        view: 'h5',
        title: 'Heading 5',
        class: 'ck-heading_heading5'
      },
      {
        model: 'heading6',
        view: 'h6',
        title: 'Heading 6',
        class: 'ck-heading_heading6'
      }
    ]
  },
  htmlSupport: {
    allow: [
      {
        name: /^.*$/,
        styles: true,
        attributes: true,
        classes: true
      }
    ]
  },
  link: {
    addTargetToExternalLinks: true,
    defaultProtocol: 'https://',
    decorators: {
      toggleDownloadable: {
        mode: 'manual',
        label: 'Downloadable',
        attributes: {
          download: 'file'
        }
      }
    },
  },
  menuBar: {
    isVisible: true
  },
  simpleUpload: {
    uploadUrl: `${environment.apiUrl}/file/ck/upload`,
  }
}
