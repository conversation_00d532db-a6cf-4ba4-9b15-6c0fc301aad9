import { inject } from '@angular/core';
import { CanActivateFn, Router, UrlTree } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { UserService } from '@/services/user.service';
import { of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export const authGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const cookieService = inject(CookieService);
  const userService = inject(UserService);

  const token = cookieService.get('token');

  if (!token) {
    router.navigate(['/login']);
    return false;
  }

  const performRedirect = (profile: any): boolean | UrlTree => {
    if (state.url === '/') {
      const userGroups = profile.groups || [];

      const childRoutes = route.routeConfig?.children || [];

      const firstAccessibleRoute = childRoutes.find(child => {
        if (!child.path || !child.component || child.redirectTo) {
          return false;
        }

        const allowedGroups = child.data?.['groups'];

        if (!allowedGroups) {
          return true;
        }

        return allowedGroups.some((group: string) => userGroups.includes(group) || userGroups.includes('ADMIN'));
      });

      if (firstAccessibleRoute) {
        return router.createUrlTree([firstAccessibleRoute.path]);
      } else {
        return router.createUrlTree(['/access-denied']);
      }
    }

    return true;
  };

  if (userService.profile) {
    return performRedirect(userService.profile);
  }

  return userService.getProfile().pipe(
    map(profile => {
      if (!profile) {
        router.navigate(['/login']);
        return false;
      }
      return performRedirect(profile);
    }),
    catchError(() => {
      router.navigate(['/login']);
      return of(false);
    })
  );
};
