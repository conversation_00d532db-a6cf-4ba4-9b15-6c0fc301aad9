import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { EMPTY, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { UserService } from '@/services/user.service';

export const profileResolver = () => {
  const userService = inject(UserService);
  const router = inject(Router);

  const profile = userService.profile;
  if (profile) {
    return of(profile);
  }

  return userService.getProfile().pipe(
    map(profile => {
      if (profile) {
        return profile;
      }
      router.navigate(['/login']);
      return EMPTY;
    }),
    catchError(() => {
      router.navigate(['/login']);
      return EMPTY;
    })
  );
};
