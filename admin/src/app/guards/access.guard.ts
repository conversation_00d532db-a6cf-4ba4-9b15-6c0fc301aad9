import { CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import {UserService} from "@/services/user.service";

export const accessGuard: CanActivateFn = (route) => {
  const router = inject(Router);
  const userService = inject(UserService);
  const profile = userService.profile;
  const allowedGroups = route.data?.['groups'] || [];
  const userGroups = profile?.groups || [];

  const hasAccess = allowedGroups.some((group: any) => userGroups.includes(group) || userGroups.includes('ADMIN'));

  return hasAccess ? true : router.createUrlTree(['/access-denied']);
};
