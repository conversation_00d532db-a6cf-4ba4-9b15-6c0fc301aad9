import { Component, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';

@Component({
    selector: 'app-root',
    imports: [RouterOutlet],
    templateUrl: './app.component.html',
    styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {

    constructor(@Inject(PLATFORM_ID) private platformId: any) {}

    ngOnInit() {
        // Ensure light theme is applied by removing any dark class
        if (isPlatformBrowser(this.platformId)) {
            document.documentElement.classList.remove('dark');
            document.body.classList.remove('dark');
        }
    }
}
