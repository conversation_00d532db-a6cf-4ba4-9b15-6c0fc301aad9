.admin-header {
  @apply bg-white dark:bg-[#0e1726] border-b border-gray-200 dark:border-gray-700 px-6 py-4;
  position: sticky;
  top: 0;
  z-index: 40;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.admin-header-container {
  @apply flex items-center justify-between max-w-full;
}

.admin-header-left {
  @apply flex items-center;
}

.admin-header-right {
  @apply flex items-center space-x-4;
}

.admin-header-user {
  @apply relative flex items-center space-x-3;
}

.admin-header-user-info {
  @apply hidden sm:flex flex-col items-end text-right;
}

.admin-header-user-email {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.admin-header-user-name {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.admin-header-user-trigger {
  @apply flex items-center space-x-2 cursor-pointer rounded-lg p-2 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

.admin-header-avatar {
  @apply w-8 h-8 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600;
}

.admin-header-avatar-fallback {
  @apply w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-medium;
}

.admin-header-dropdown-arrow {
  @apply text-gray-400 dark:text-gray-500 transition-transform duration-200;
  
  &.rotated {
    @apply rotate-180;
  }
}

.admin-header-dropdown {
  @apply absolute right-0 top-full mt-2 w-72 bg-white dark:bg-[#0e1726] rounded-lg shadow-lg border border-gray-200 dark:border-gray-700;
  @apply py-2 z-50;
  animation: fadeInDown 0.2s ease-out;
}

.admin-header-dropdown-header {
  @apply px-4 py-3;
}

.admin-header-dropdown-user-info {
  @apply flex items-center space-x-3;
}

.admin-header-dropdown-avatar {
  @apply w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600;
}

.admin-header-dropdown-avatar-fallback {
  @apply w-10 h-10 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-medium;
}

.admin-header-dropdown-text {
  @apply flex-1 min-w-0;
}

.admin-header-dropdown-name {
  @apply text-sm font-medium text-gray-900 dark:text-white truncate;
}

.admin-header-dropdown-email {
  @apply text-xs text-gray-500 dark:text-gray-400 truncate;
}

.admin-header-dropdown-divider {
  @apply border-t border-gray-200 dark:border-gray-700 mx-2;
}

.admin-header-dropdown-menu {
  @apply py-1;
}

.admin-header-dropdown-item {
  @apply w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200;
  @apply focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-800;
  
  svg {
    @apply text-gray-400 dark:text-gray-500;
  }
  
  &:hover svg {
    @apply text-gray-600 dark:text-gray-300;
  }
}

.admin-header-loading {
  @apply flex items-center space-x-3;
}

.admin-header-skeleton {
  @apply w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse;
}

// Responsive design
@media (max-width: 640px) {
  .admin-header {
    @apply px-4 py-3;
  }
  
  .admin-header-user-info {
    @apply hidden;
  }
  
  .admin-header-dropdown {
    @apply w-64;
  }
}

// Animation keyframes
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dark theme specific adjustments
.dark {
  .admin-header-dropdown {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  }
}
