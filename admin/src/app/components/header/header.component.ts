import { Component, inject, signal, computed, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { UserService } from '@/services/user.service';
import { AuthService } from '@/services/auth.service';
import { environment } from '../../../environments/environment';

export interface UserProfile {
  id?: number;
  firstName?: string;
  lastName?: string;
  email: string;
  avatar?: {
    id: number;
    name: string;
    originalName: string;
    path: string;
  } | null;
  groups?: string[];
}

@Component({
  selector: 'admin-header',
  imports: [CommonModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdminHeaderComponent implements OnInit {
  private userService = inject(UserService);
  private authService = inject(AuthService);
  private router = inject(Router);

  // Signals for reactive state management
  isDropdownOpen = signal<boolean>(false);
  userProfile = signal<UserProfile | null>(null);
  
  // Computed values
  userDisplayName = computed(() => {
    const profile = this.userProfile();
    if (!profile) return '';
    return profile.firstName || profile.email.split('@')[0];
  });

  userInitials = computed(() => {
    const profile = this.userProfile();
    if (!profile) return '';
    
    if (profile.firstName && profile.lastName) {
      return `${profile.firstName.charAt(0)}${profile.lastName.charAt(0)}`.toUpperCase();
    } else if (profile.firstName) {
      return profile.firstName.charAt(0).toUpperCase();
    } else {
      return profile.email.charAt(0).toUpperCase();
    }
  });

  avatarUrl = computed(() => {
    const profile = this.userProfile();
    if (!profile?.avatar?.name) return null;
    return `${environment.serverUrl}/upload/${profile.avatar.name}`;
  });

  ngOnInit() {
    // Load user profile on component initialization
    this.loadUserProfile();
  }

  private loadUserProfile() {
    this.userService.getProfile().subscribe({
      next: () => {
        // UserService stores profile data in its own signals
        this.userProfile.set(this.userService.profile);
      },
      error: (error) => {
        console.error('Failed to load user profile:', error);
      }
    });
  }

  toggleDropdown(event: Event) {
    event.stopPropagation();
    this.isDropdownOpen.set(!this.isDropdownOpen());
  }

  closeDropdown() {
    this.isDropdownOpen.set(false);
  }

  onLogout() {
    this.authService.logout();
    this.closeDropdown();
  }

  // Handle clicks outside dropdown to close it
  onDocumentClick(event: Event) {
    if (!this.isDropdownOpen()) return;
    
    const target = event.target as HTMLElement;
    const dropdown = document.querySelector('.admin-header-dropdown');
    const trigger = document.querySelector('.admin-header-user-trigger');
    
    if (dropdown && trigger && 
        !dropdown.contains(target) && 
        !trigger.contains(target)) {
      this.closeDropdown();
    }
  }
}
