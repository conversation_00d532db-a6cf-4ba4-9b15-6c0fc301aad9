<header class="admin-header" (document:click)="onDocumentClick($event)">
  <div class="admin-header-container">
    <!-- Left side - could be used for breadcrumbs or page title in the future -->
    <div class="admin-header-left">
      <!-- Reserved for future use -->
    </div>

    <!-- Right side - User information and controls -->
    <div class="admin-header-right">
      @if (userProfile(); as profile) {
        <div class="admin-header-user">
          <!-- User info display -->
          <div class="admin-header-user-info">
            <span class="admin-header-user-email">{{ profile.email }}</span>
            @if (userDisplayName()) {
              <span class="admin-header-user-name">{{ userDisplayName() }}</span>
            }
          </div>

          <!-- User avatar and dropdown trigger -->
          <div class="admin-header-user-trigger" 
               (click)="toggleDropdown($event)"
               [attr.aria-expanded]="isDropdownOpen()"
               [attr.aria-haspopup]="true"
               role="button"
               tabindex="0"
               (keydown.enter)="toggleDropdown($event)"
               (keydown.space)="toggleDropdown($event)">
            
            @if (avatarUrl()) {
              <img [src]="avatarUrl()" 
                   [alt]="'Avatar of ' + userDisplayName()"
                   class="admin-header-avatar"
                   loading="lazy">
            } @else {
              <div class="admin-header-avatar-fallback"
                   [attr.aria-label]="'Avatar of ' + userDisplayName()">
                {{ userInitials() }}
              </div>
            }

            <!-- Dropdown arrow -->
            <svg class="admin-header-dropdown-arrow" 
                 [class.rotated]="isDropdownOpen()"
                 width="16" 
                 height="16" 
                 viewBox="0 0 24 24" 
                 fill="none" 
                 stroke="currentColor" 
                 stroke-width="2">
              <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
          </div>

          <!-- Dropdown menu -->
          @if (isDropdownOpen()) {
            <div class="admin-header-dropdown"
                 role="menu"
                 [attr.aria-label]="'User menu for ' + userDisplayName()">
              
              <div class="admin-header-dropdown-header">
                <div class="admin-header-dropdown-user-info">
                  @if (avatarUrl()) {
                    <img [src]="avatarUrl()" 
                         [alt]="'Avatar of ' + userDisplayName()"
                         class="admin-header-dropdown-avatar"
                         loading="lazy">
                  } @else {
                    <div class="admin-header-dropdown-avatar-fallback">
                      {{ userInitials() }}
                    </div>
                  }
                  <div class="admin-header-dropdown-text">
                    @if (userDisplayName()) {
                      <div class="admin-header-dropdown-name">{{ userDisplayName() }}</div>
                    }
                    <div class="admin-header-dropdown-email">{{ profile.email }}</div>
                  </div>
                </div>
              </div>

              <div class="admin-header-dropdown-divider"></div>

              <div class="admin-header-dropdown-menu">
                <button type="button" 
                        class="admin-header-dropdown-item"
                        (click)="onLogout()"
                        role="menuitem">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16,17 21,12 16,7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                  </svg>
                  Выйти
                </button>
              </div>
            </div>
          }
        </div>
      } @else {
        <!-- Loading state or no user -->
        <div class="admin-header-loading">
          <div class="admin-header-skeleton"></div>
        </div>
      }
    </div>
  </div>
</header>
