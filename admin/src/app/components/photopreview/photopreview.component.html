<div class="photo-list" cdkDropList (cdkDropListDropped)="drop($event)">
  @for(item of items; track item.id) {
    <div class="photo-item" cdkDrag>
      <div class="photo-item__drag" cdkDragHandle>
        <img src="/drop.png" alt="" style="width: 24px; margin-right: 20px; cursor: pointer">
      </div>
      <div (click)="showPreview(environment.serverUrl + '/upload/' + item.name)" class="photo-img cursor-pointer">
        <img [src]="environment.serverUrl + '/upload/' + item.name" alt="">
      </div>
      @if (hasDescription) {
        <div class="flex items-center">
          <div class="photo-description">
            <input type="text" placeholder="Описание фотографии" [(ngModel)]="item.description">
          </div>
          <span title="Название" class="cursor-pointer mx-2">
            &#9432;
          </span>
        </div>
      }
      @if(!disableRemoveBtn) {
        <div class="photo-delete">
          <button class="btn btn-danger btn-sm ml-5" (click)="deleteFile(item.id)">Удалить</button>
        </div>
      }
    </div>
  }
</div>
@if (previewImage) {
  <div class="modal_" (click)="closePreview()">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <div class="relative">
        <img [src]="previewImage" alt="Preview" />
        <button class="close-btn" (click)="closePreview()">✖</button>
      </div>
    </div>
  </div>
}
