.photo-img img {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.photo-item {
  display: flex;
  align-items: center;
  padding: 15px 0px;
}

.photo-description input {
  border: 1px solid #e8e8e8;
  padding: 5px 10px;
  border-radius: 4px;
  margin-left: 20px;
  min-width: 500px;
}

.modal_ {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000 !important;
}

.modal-content {
  position: relative;
  display: flex;
  justify-content: center;
  width: 80%;
  height: 80%;
}

.modal-content img {
  max-width: 100%;
  height: 100%;
  border-radius: 5px;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: red;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
}