import {Component, EventEmitter, inject, Input, NgModule, Output} from '@angular/core';
import {environment} from "../../../environments/environment";
import {FileService} from "@/services/file.service";
import {FormsModule, NgModel} from "@angular/forms";
import {CdkDragDrop, DragDropModule, moveItemInArray} from '@angular/cdk/drag-drop';


@Component({
    selector: 'PhotoPreview',
    imports: [
        FormsModule,
        DragDropModule
    ],
    templateUrl: './photopreview.component.html',
    styleUrl: './photopreview.component.scss'
})
export class PhotopreviewComponent {
  fileService = inject(FileService)
  @Input() items: any = []
  @Input() hasDescription: boolean = true;
  @Input() disableRemoveBtn: boolean = false;
  @Output() onItemRemoved: EventEmitter<any> = new EventEmitter();
  previewImage: string | null = null;
  protected readonly environment = environment;

  ngOnInit() {
    this.items = this.items.sort((a: any, b: any) => a.sort - b.sort);
  }

  showPreview(image: string): void {
    this.previewImage = image;
  }

  closePreview(): void {
    this.previewImage = null;
  }

  deleteFile(id: number) {
    const index = this.items.findIndex((item: any) => item.id === id)
    if(!id) {
      this.items.splice(index, 1)
      this.onItemRemoved.emit()
      return
    }
    return this.fileService.delete(id).subscribe(() => {
      this.items.splice(index, 1)
      this.onItemRemoved.emit(id)
    })
  }

  changeDescription(id: number, description: string) {
    return this.fileService.changeDescription(id, description).subscribe()
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.items, event.previousIndex, event.currentIndex);
    this.items.forEach((item: any, index: number) => {
      item.sort = index + 1
    })
    this.fileService.changeSort(this.items).subscribe()
  }

}
