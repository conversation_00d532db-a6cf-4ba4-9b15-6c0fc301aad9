<dialog #carouselFormDialog class="admin-modal" style="min-width: 600px">
  <div class="admin-modal-content">
    <form [formGroup]="carouselForm" class="space-y-3">
    <div>
      <label>Заголовок</label>
      <input type="text" formControlName="title" class="form-input">
    </div>
    <div>
      <label>Размер</label>
      <select formControlName="size" class="form-select">
        @for(size of carouselSizes; track $index) {
          <option [ngValue]="size.value">{{size.title}}</option>
        }
      </select>
    </div>
    <div>
      <label>Тип</label>
      <select formControlName="type" class="form-select">
        <option value="carousel">Карусель</option>
        <option value="list">Список</option>
      </select>
    </div>
    <div>
      <label>Элементы</label>
      <button class="btn btn-primary btn-sm" (click)="showCarouselItemForm()">Добавить</button>
      <table class="mt-5" *ngIf="carouselForm.get('items')!.value.length">
        <thead>
        <tr>
          <th>Дата</th>
          <th>Текст</th>
          <th></th>
        </tr>
        </thead>
        <tbody>
        @for(carouselItem of carouselForm.get('items')!.value; track $index) {
          <tr>
            <td>{{carouselItem.date}}</td>
            <td>{{carouselItem.text}}</td>
            <td>
              <div class="flex gap-2">
                <button class="btn btn-primary btn-sm" (click)="editCarouselItem($index, carouselItem)">Редактировать</button>
                <button class="btn btn-danger btn-sm" (click)="removeCarouselItem($index)">Удалить</button>
              </div>
            </td>
          </tr>
        }
        </tbody>
      </table>
    </div>
    <div class="admin-modal-footer">
      <button class="btn btn-primary btn-sm" (click)="saveCarouselForm()" [disabled]="carouselForm.invalid">Сохранить</button>
      <button class="btn btn-outline-secondary btn-sm" (click)="closeCarouselForm()">Закрыть</button>
    </div>
  </form>
  </div>
</dialog>

<dialog #carouselItemFormDialog class="admin-modal">
  <div class="admin-modal-content">
    <form [formGroup]="carouselFormItem" class="space-y-3">
    <div>
      <div class="flex items-center">
        <input formControlName="show" type="checkbox" class="form-checkbox">
        Показывать
        <span title="Показывать" class="cursor-pointer mx-2">
          &#9432;
        </span>
      </div>
    </div>
      <div>
        <label>Название</label>
        <div [ngClass]="{ 'has-error': carouselFormItem.get('title')?.invalid && carouselFormItem.get('title')?.touched }" class="flex items-center">
          <input required formControlName="title" type="text" class="form-input"><span class="asterix">*</span>
          <span title="Название" class="cursor-pointer mx-2">
          &#9432;
        </span>
        </div>
      </div>
      <div>
        <label>Тег</label>
        <div [ngClass]="{ 'has-error': carouselFormItem.get('tag')?.invalid && carouselFormItem.get('tag')?.touched }" class="flex items-center">
          <input required formControlName="tag" type="text" class="form-input"><span class="asterix">*</span>
          <span title="Тег" class="cursor-pointer mx-2">
          &#9432;
        </span>
        </div>
      </div>
    <div>
      <label>Ссылка</label>
      <div [ngClass]="{ 'has-error': carouselFormItem.get('link')?.invalid && carouselFormItem.get('link')?.touched }" class="flex items-center">
        <input required formControlName="link" type="text" class="form-input"><span class="asterix">*</span>
        <span title="Ссылка" class="cursor-pointer mx-2">
          &#9432;
        </span>
      </div>
    </div>
    <div>
      <label>Дата</label>
      <div [ngClass]="{ 'has-error': carouselFormItem.get('date')?.invalid && carouselFormItem.get('date')?.touched }" class="flex items-center">
        <input required formControlName="date" type="date" class="form-input"><span class="asterix">*</span>
        <span title="Дата" class="cursor-pointer mx-2">
          &#9432;
        </span>
      </div>
    </div>
    <div [ngClass]="{ 'has-error': carouselFormItem.get('text')?.invalid && carouselFormItem.get('text')?.touched }">
      <label>Текст</label>
      <div  class="flex items-center">
        <textarea required formControlName="text" class="form-textarea"></textarea><span class="asterix">*</span>
        <span title="Текст" class="cursor-pointer mx-2">
          &#9432;
        </span>
      </div>
    </div>
    <div class="flex flex-col">
      <label>Изображение</label>
      <div [ngClass]="{ 'has-error': carouselFormItem.get('image')?.invalid && carouselFormItem.get('image')?.touched }" class="flex items-center">
        <input #carouselItemImage required type="file"  accept="image/*" class="form-input" (change)="uploadFile($event, 'image', false)"><span class="asterix">*</span>
        <span title="Изображение" class="cursor-pointer mx-2">
          &#9432;
        </span>
      </div>
      @if(carouselFormItem.value.image) {
        <PhotoPreview [disableRemoveBtn]="true" (onItemRemoved)="onPreviewRemoved()" [items]="[carouselFormItem.value.image]"/>
      }
    </div>
    <div class="admin-modal-footer">
      <button [disabled]="carouselFormItem.invalid" type="submit" class="btn btn-primary btn-sm" (click)="addCarouselItem()">Сохранить</button>
      <button class="btn btn-outline-secondary btn-sm" (click)="closeCarouselItemForm()">Закрыть</button>
    </div>
  </form>
  </div>
</dialog>
