import { Component, inject } from '@angular/core';
import { UserService } from "../../services/user.service";
import { CommonModule } from "@angular/common";
import { firstValueFrom } from "rxjs";
import { RouterOutlet } from '@angular/router'
import { SidebarComponent } from '@/components/sidebar/sidebar.component'
import { AdminHeaderComponent } from '@/components/header/header.component'

@Component({
    selector: 'app-layout',
    imports: [CommonModule, RouterOutlet, SidebarComponent, AdminHeaderComponent],
    templateUrl: './layout.component.html',
    styleUrl: './layout.component.scss'
})
export class LayoutComponent {
}
