import {Component, ElementRef, EventEmitter, inject, Input, Output, ViewChild} from '@angular/core';
import {FormBuilder, ReactiveFormsModule, Validators} from "@angular/forms";
import {ConstructorService} from "@/services/constructor.service";
import {CommonModule, JsonPipe} from "@angular/common";
import {FileService} from "@/services/file.service";
import {PhotopreviewComponent} from "@/components/photopreview/photopreview.component";
import {PhotoService} from "@/services/photo.service";

@Component({
  selector: 'ConstructorItem',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    JsonPipe,
    CommonModule,
    PhotopreviewComponent
  ],
  templateUrl: './constructor-item.component.html',
  styleUrl: './constructor-item.component.scss'
})
export class ConstructorItemComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  message: string = "";
  @Input() add: boolean = false;
  @Input() item: any
  @Output() onItemAdded: EventEmitter<any> = new EventEmitter();
  @Output() onItemRemoved: EventEmitter<any> = new EventEmitter();
  @ViewChild('editForm') editForm!: ElementRef<HTMLDialogElement>;
  constructorService = inject(ConstructorService)
  fb = inject(FormBuilder)
  fileService = inject(FileService)
  form = this.fb.group({
    id: null,
    link: [null, [Validators.required, Validators.pattern('^[a-zA-Z0-9-_/.]+$')]],
    date: [null],
    text: [null],
    image: [null],
    show: [true]
  })
  showForm: boolean = false
  image: any

  ngOnInit() {
    if(this.item) this.form.patchValue(this.item)
  }

  delete(id: number) {
    this.constructorService.deleteItem(id).subscribe((res: any) => {
      this.onItemRemoved.emit(this.item.id)
    })
  }

  uploadFile(e: Event, name: string, multipe: boolean = true) {
    const target = e.target as HTMLInputElement;
    if(!target.files) return
    this.fileService.upload(target.files, 'constructor').subscribe((res: any) => {
      this.form.controls.image.patchValue(res[0])
    })
  }

  onSubmit() {
    this.showForm = false
    this.closeEditForm()
    if(this.item) {
      return this.constructorService.updateItem(this.form.value).subscribe((res: any) => {
        this.item = res;
        this.form.patchValue(this.item)
        this.openModal('Успешно обновлено.')
      })
    }
    this.constructorService.createItem(this.form.value).subscribe((res: any) => {
      this.onItemAdded.emit(res);
    })
    return false
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  showEditForm() {
    this.editForm.nativeElement.showModal()
  }

  closeEditForm() {
    this.editForm.nativeElement.close()
  }
}
