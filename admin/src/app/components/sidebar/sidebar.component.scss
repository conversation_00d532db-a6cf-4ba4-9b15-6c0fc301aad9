// Улучшенные стили для меню
.nav-item {
  margin-bottom: 0.25rem;

  a, button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    color: #374151;
    font-weight: 500;

    &:hover {
      background-color: #f3f4f6;
      color: #1f2937;

      .dark & {
        background-color: #374151;
        color: #f9fafb;
      }
    }

    &.active-link {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
      color: white !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      span {
        color: white !important;
      }
    }
  }

  // Дополнительная специфичность для активных ссылок
  a.active-link {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    color: white !important;

    span {
      color: white !important;
    }
  }
}

// Стили для аккордеона форума
.accordion.menu {
  .nav-link {
    &.active {
      background-color: #f3f4f6;

      .dark & {
        background-color: #374151;
      }
    }

    &.active-link {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
      color: white !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      span {
        color: white !important;
      }
    }
  }
}

// Стили для подменю
.sub-menu {
  padding-left: 1rem;
  margin-top: 0.5rem;

  li {
    margin-bottom: 0.25rem;

    a {
      display: block;
      padding: 0.5rem 1rem;
      border-radius: 0.375rem;
      color: #6b7280;
      font-weight: 400;
      transition: all 0.2s ease-in-out;

      &:hover {
        background-color: #f3f4f6;
        color: #374151;

        .dark & {
          background-color: #374151;
          color: #d1d5db;
        }
      }

      &.active {
        background-color: #dbeafe;
        color: #1d4ed8;
        font-weight: 500;

        .dark & {
          background-color: #1e3a8a;
          color: #93c5fd;
        }
      }
    }
  }
}

// Стили для иконок удалены, так как иконки убраны из меню

// Стили для сайдбара
.sidebar {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  border-right: 1px solid #e5e7eb;

  .dark & {
    background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
    border-right-color: #374151;
  }
}

// Стили для логотипа
.main-logo {
  padding: 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: #f3f4f6;

    .dark & {
      background-color: #374151;
    }
  }

  span {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
  }
}