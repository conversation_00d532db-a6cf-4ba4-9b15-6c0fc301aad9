import {Component, inject, OnInit, OnDestroy} from '@angular/core';
import { NgScrollbar } from 'ngx-scrollbar';
import { IconModule } from "@/shared/icon/icon.module";
import {RouterLink, Router, NavigationEnd} from "@angular/router";
import {CommonModule} from "@angular/common";
import {GroupsEnum, UserService} from "@/services/user.service";
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
    selector: 'sidebar',
    imports: [NgScrollbar, IconModule, RouterLink, CommonModule],
    templateUrl: './sidebar.component.html',
    styleUrl: './sidebar.component.scss'
})
export class SidebarComponent implements OnInit, OnDestroy {
  router = inject(Router);
  userService = inject(UserService);
  activeDropdown: string[] = [];
  public GroupsEnum = GroupsEnum;
  private routerSubscription?: Subscription;

  ngOnInit() {
    // Автоматически открываем форум если мы на странице форума
    if (this.isForumActive()) {
      this.activeDropdown.push('forum');
    }

    // Подписываемся на изменения роутера для обновления активных элементов
    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        // Обновляем состояние форума при изменении роута
        if (this.isForumActive() && !this.activeDropdown.includes('forum')) {
          this.activeDropdown.push('forum');
        } else if (!this.isForumActive() && this.activeDropdown.includes('forum')) {
          this.activeDropdown = this.activeDropdown.filter(d => d !== 'forum');
        }
      });
  }

  ngOnDestroy() {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  isActiveRoute(route: string): boolean {
    const currentUrl = this.router.url;

    // Убираем query параметры и фрагменты
    const cleanUrl = currentUrl.split('?')[0].split('#')[0];

    // Проверяем точное совпадение или начало пути
    return cleanUrl === `/${route}` || cleanUrl.startsWith(`/${route}/`);
  }

  isForumActive(): boolean {
    return this.router.url.startsWith('/forum');
  }

  toggleAccordion(name: string, parent?: string) {
    if (this.activeDropdown.includes(name)) {
      this.activeDropdown = this.activeDropdown.filter((d) => d !== name);
    } else {
      this.activeDropdown.push(name);
    }
  }
}
