<div class="space-y-8">
    <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
        <div class="rounded-full bg-primary p-1.5 text-white ring-2 ring-primary/30 ltr:mr-3 rtl:ml-3">
            <icon-bell />
        </div>
        <span class="ltr:mr-3 rtl:ml-3">Documentation: </span>
        <a href="https://www.npmjs.com/package/@bhplugin/ng-datatable" target="_blank" class="block hover:underline">
            {{"https://www.npmjs.com/package/@bhplugin/ng-datatable"}}
        </a>
    </div>
    <div class="panel pb-0">
        <div class="mb-5 flex flex-col gap-5 md:flex-row md:items-center">
            <h5 class="text-lg font-semibold dark:text-white-light">Show/Hide Columns</h5>

            <div class="flex items-center gap-5 ltr:ml-auto rtl:mr-auto">
                <div hlMenu class="dropdown">
                    <button
                        hlMenuButton
                        type="button"
                        class="flex items-center rounded-md border border-[#e0e6ed] px-4 py-2 text-sm font-semibold dark:border-[#253b5c] dark:bg-[#1b2e4b] dark:text-white-dark"
                    >
                        <span class="ltr:mr-1 rtl:ml-1">Columns</span>
                        <icon-caret-down class="h-5 w-5" />
                    </button>
                    <ul *hlMenuItems @toggleAnimation class="w-max ltr:left-0 rtl:right-0">
                        <ng-container *ngFor="let col of cols;index as i;">
                            <li>
                                <div class="flex items-center px-4 py-1">
                                    <label [for]="'chk-' + i" class="mb-0 cursor-pointer">
                                        <input
                                            type="checkbox"
                                            [id]="'chk-' + i"
                                            class="form-checkbox"
                                            [ngModel]="!col.hide"
                                            (ngModelChange)="col.hide = !$event"
                                            (change)="updateColumn(col)"
                                        />
                                        <span class="ltr:ml-2 rtl:mr-2">{{ col.title }}</span>
                                    </label>
                                </div>
                            </li>
                        </ng-container>
                    </ul>
                </div>
                <div>
                    <input [(ngModel)]="search" type="text" class="form-input" placeholder="Search..." />
                </div>
            </div>
        </div>

        <div class="datatable">
            <ng-datatable
                [rows]="rows"
                [columns]="cols"
                [sortable]="true"
                [search]="search"
                skin="whitespace-nowrap table-hover"
                firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
                lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg> '
                previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
                nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
            >
                <ng-template slot="dob" let-value="data"> {{ formatDate(value.dob) }} </ng-template>
                <ng-template slot="isActive" let-value="data">
                    <span class="capitalize" [ngClass]="value.isActive ? 'text-success' : 'text-danger'">{{ value.isActive }}</span>
                </ng-template>
            </ng-datatable>
        </div>
    </div>
</div>
