<div>
    <div class="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
        <div class="rounded-full bg-primary p-1.5 text-white ring-2 ring-primary/30 ltr:mr-3 rtl:ml-3">
            <icon-bell />
        </div>
        <span class="ltr:mr-3 rtl:ml-3">Documentation: </span>
        <a href="https://www.npmjs.com/package/@bhplugin/ng-datatable" target="_blank" class="block hover:underline">
            {{"https://www.npmjs.com/package/@bhplugin/ng-datatable"}}
        </a>
    </div>

    <div class="panel mt-6 pb-0">
        <div class="mb-5 flex flex-col gap-5 md:flex-row md:items-center">
            <h5 class="text-lg font-semibold dark:text-white-light">Table 1</h5>
            <div class="ltr:ml-auto rtl:mr-auto">
                <input [(ngModel)]="search1" type="text" class="form-input w-auto" placeholder="Search..." />
            </div>
        </div>

        <div class="datatable">
            <ng-datatable
                [rows]="rows"
                [columns]="datatable1Cols"
                [sortable]="true"
                sortColumn="firstName"
                [search]="search1"
                skin="whitespace-nowrap table-hover"
                firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
                lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg> '
                previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
                nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
            >
                <ng-template slot="firstName" let-value="data">
                    <div class="flex w-max items-center">
                        <img class="h-9 w-9 rounded-full object-cover ltr:mr-2 rtl:ml-2" [src]="'/assets/images/profile-' + value.id + '.jpeg'" />
                        {{ value.firstName + ' ' + value.lastName }}
                    </div>
                </ng-template>
                <ng-template slot="dob" let-value="data"> {{ formatDate(value.dob) }} </ng-template>
                <ng-template slot="status" let-value="data">
                    <span class="badge" [ngClass]="'bg-' + value.statusColor">{{ value.status }}</span>
                </ng-template>
                <ng-template slot="action" let-value>
                    <div class="text-center">
                        <button type="button" ngxTippy="Delete">
                            <icon-x-circle />
                        </button>
                    </div>
                </ng-template>
            </ng-datatable>
        </div>
    </div>

    <div class="panel mt-6 pb-0">
        <div class="mb-5 flex flex-col gap-5 md:flex-row md:items-center">
            <h5 class="text-lg font-semibold dark:text-white-light">Table 2</h5>
            <div class="ltr:ml-auto rtl:mr-auto">
                <input [(ngModel)]="search2" type="text" class="form-input w-auto" placeholder="Search..." />
            </div>
        </div>

        <div class="datatable">
            <ng-datatable
                [rows]="rows"
                [columns]="datatable2Cols"
                [sortable]="true"
                sortColumn="firstName"
                [search]="search2"
                skin="whitespace-nowrap table-hover"
                firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180">
<path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>'
                lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180">
<path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
'
                previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180">
<path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>'
                nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180">
<path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>'
            >
                <ng-template slot="firstName" let-value="data">
                    <div class="flex w-max items-center">
                        <img class="h-9 w-9 rounded-full object-cover ltr:mr-2 rtl:ml-2" [src]="'/assets/images/profile-' + value.id + '.jpeg'" />
                        {{ value.firstName + ' ' + value.lastName }}
                    </div>
                </ng-template>
                <ng-template slot="age" let-value="data">
                    <div class="flex h-2.5 w-4/5 min-w-[100px] rounded-full bg-[#ebedf2] dark:bg-dark/40">
                        <div
                            class="h-2.5 rounded-full rounded-bl-full text-center text-xs text-white"
                            [ngClass]="'bg-' + value.statusColor"
                            [ngStyle]="{width: value.age + '%'}"
                        ></div>
                    </div>
                </ng-template>
                <ng-template slot="dob" let-value="data"> {{ formatDate(value.dob) }} </ng-template>
                <ng-template slot="action">
                    <div class="flex items-center">
                        <div>
                            <button type="button" class="ltr:mr-2 rtl:ml-2" ngxTippy="Edit">
                                <icon-pencil />
                            </button>
                        </div>
                        <div>
                            <button type="button" ngxTippy="Delete">
                                <icon-trash-lines />
                            </button>
                        </div>
                    </div>
                </ng-template>
            </ng-datatable>
        </div>
    </div>
</div>
